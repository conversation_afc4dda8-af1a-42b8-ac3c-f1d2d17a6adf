@echo off
echo ========================================
echo Building Frontend-Only Subscriber App
echo ========================================
echo.

cd /d "%~dp0"

echo Attempting to build APK using available tools...
echo.

REM Try to find Android Studio's gradle
set ANDROID_STUDIO_GRADLE=""
if exist "C:\Program Files\Android\Android Studio\gradle\gradle-8.2\bin\gradle.bat" (
    set ANDROID_STUDIO_GRADLE="C:\Program Files\Android\Android Studio\gradle\gradle-8.2\bin\gradle.bat"
    echo Found Android Studio Gradle at: %ANDROID_STUDIO_GRADLE%
) else if exist "C:\Program Files\Android\Android Studio\gradle\gradle-8.0\bin\gradle.bat" (
    set ANDROID_STUDIO_GRADLE="C:\Program Files\Android\Android Studio\gradle\gradle-8.0\bin\gradle.bat"
    echo Found Android Studio Gradle at: %ANDROID_STUDIO_GRADLE%
) else if exist "C:\Program Files\Android\Android Studio\gradle\gradle-7.6\bin\gradle.bat" (
    set ANDROID_STUDIO_GRADLE="C:\Program Files\Android\Android Studio\gradle\gradle-7.6\bin\gradle.bat"
    echo Found Android Studio Gradle at: %ANDROID_STUDIO_GRADLE%
)

if not %ANDROID_STUDIO_GRADLE%=="" (
    echo Building with Android Studio Gradle...
    %ANDROID_STUDIO_GRADLE% assembleDebug
    if %ERRORLEVEL% equ 0 goto copy_apk
)

REM Try system gradle
gradle --version >nul 2>&1
if not errorlevel 1 (
    echo Building with system Gradle...
    gradle assembleDebug
    if %ERRORLEVEL% equ 0 goto copy_apk
)

echo.
echo ========================================
echo Manual Build Instructions
echo ========================================
echo.
echo Since automated build tools are not available, please:
echo.
echo 1. Open Android Studio
echo 2. Open this project folder: %CD%
echo 3. Click "Build" menu > "Build Bundle(s) / APK(s)" > "Build APK(s)"
echo 4. Wait for build to complete
echo 5. Click "locate" in the build notification
echo 6. Copy the APK to your Desktop
echo.
echo The APK will be located at:
echo %CD%\app\build\outputs\apk\debug\app-debug.apk
echo.
echo Demo Login Credentials:
echo - admin / password123
echo - user / demo123  
echo - test / test123
echo - guest / guest123
echo.
goto end

:copy_apk
echo.
echo Step 2: Copying APK to Desktop...

REM Create destination folder on Desktop
set DESKTOP=%USERPROFILE%\Desktop
set APK_FOLDER=%DESKTOP%\SubscriberApp_APK
if not exist "%APK_FOLDER%" mkdir "%APK_FOLDER%"

REM Copy the APK file
set SOURCE_APK=app\build\outputs\apk\debug\app-debug.apk
set DEST_APK=%APK_FOLDER%\SubscriberApp-Frontend-Only.apk

if exist "%SOURCE_APK%" (
    copy "%SOURCE_APK%" "%DEST_APK%"
    echo.
    echo ========================================
    echo ✅ BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location: %DEST_APK%
    echo.
    echo The APK has been saved to your Desktop in the folder:
    echo %APK_FOLDER%
    echo.
    echo Demo Login Credentials:
    echo - admin / password123
    echo - user / demo123  
    echo - test / test123
    echo - guest / guest123
    echo.
    
    REM Open the folder containing the APK
    explorer "%APK_FOLDER%"
    
) else (
    echo ❌ APK file not found at: %SOURCE_APK%
    echo Build may have failed.
)

:end
echo.
echo Press any key to exit...
pause >nul

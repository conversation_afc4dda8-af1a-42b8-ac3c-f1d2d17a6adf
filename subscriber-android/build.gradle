// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        compose_version = '1.5.8'
        kotlin_version = '1.9.22'
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

plugins {
    id 'com.android.application' version '8.2.2' apply false
    id 'com.android.library' version '8.2.2' apply false
    id 'org.jetbrains.kotlin.android' version '1.9.22' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

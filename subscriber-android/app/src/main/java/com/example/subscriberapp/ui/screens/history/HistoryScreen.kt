package com.example.subscriberapp.ui.screens.history

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.components.NeonCard
import java.text.SimpleDateFormat
import java.util.*

data class AttendanceRecord(
    val id: String,
    val method: CheckInMethod,
    val timestamp: Date,
    val location: String,
    val status: AttendanceStatus,
    val duration: String? = null
)

enum class CheckInMethod(val displayName: String, val icon: ImageVector, val color: Color) {
    QR("QR Code", Icons.Default.QrCode, Color(0xFFE94560)),
    NFC("NFC Card", Icons.Default.Nfc, Color(0xFF4ECDC4)),
    WIFI("WiFi", Icons.Default.Wifi, Color(0xFF667eea)),
    BLUETOOTH("Bluetooth", Icons.Default.Bluetooth, Color(0xFF2196F3))
}

enum class AttendanceStatus(val displayName: String, val color: Color) {
    SUCCESS("Success", Color(0xFF4CAF50)),
    EXPIRED("Expired", Color(0xFFFF9800)),
    INVALID("Invalid", Color(0xFFF44336))
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HistoryScreen(
    onNavigateBack: () -> Unit
) {
    // Mock attendance records
    val attendanceRecords = remember {
        listOf(
            AttendanceRecord(
                id = "1",
                method = CheckInMethod.QR,
                timestamp = Date(System.currentTimeMillis() - 3600000), // 1 hour ago
                location = "Main Office",
                status = AttendanceStatus.SUCCESS,
                duration = "8h 30m"
            ),
            AttendanceRecord(
                id = "2",
                method = CheckInMethod.NFC,
                timestamp = Date(System.currentTimeMillis() - 7200000), // 2 hours ago
                location = "Conference Room A",
                status = AttendanceStatus.SUCCESS,
                duration = "2h 15m"
            ),
            AttendanceRecord(
                id = "3",
                method = CheckInMethod.WIFI,
                timestamp = Date(System.currentTimeMillis() - 86400000), // 1 day ago
                location = "Remote Office",
                status = AttendanceStatus.EXPIRED,
                duration = null
            ),
            AttendanceRecord(
                id = "4",
                method = CheckInMethod.BLUETOOTH,
                timestamp = Date(System.currentTimeMillis() - 172800000), // 2 days ago
                location = "Meeting Room B",
                status = AttendanceStatus.SUCCESS,
                duration = "4h 45m"
            ),
            AttendanceRecord(
                id = "5",
                method = CheckInMethod.QR,
                timestamp = Date(System.currentTimeMillis() - 259200000), // 3 days ago
                location = "Main Office",
                status = AttendanceStatus.INVALID,
                duration = null
            ),
            AttendanceRecord(
                id = "6",
                method = CheckInMethod.NFC,
                timestamp = Date(System.currentTimeMillis() - 345600000), // 4 days ago
                location = "Lab 1",
                status = AttendanceStatus.SUCCESS,
                duration = "6h 20m"
            )
        )
    }
    
    var selectedFilter by remember { mutableStateOf("All") }
    val filters = listOf("All", "Success", "Expired", "Invalid")
    
    val filteredRecords = when (selectedFilter) {
        "Success" -> attendanceRecords.filter { it.status == AttendanceStatus.SUCCESS }
        "Expired" -> attendanceRecords.filter { it.status == AttendanceStatus.EXPIRED }
        "Invalid" -> attendanceRecords.filter { it.status == AttendanceStatus.INVALID }
        else -> attendanceRecords
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Attendance History",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Statistics Overview
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    HistoryStat("42", "Total", Color(0xFF00BCD4))
                    HistoryStat("38", "Success", Color(0xFF4CAF50))
                    HistoryStat("3", "Expired", Color(0xFFFF9800))
                    HistoryStat("1", "Invalid", Color(0xFFF44336))
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Filter Chips
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                filters.forEach { filter ->
                    FilterChip(
                        selected = filter == selectedFilter,
                        onClick = { selectedFilter = filter },
                        label = { Text(filter) },
                        colors = FilterChipDefaults.filterChipColors(
                            selectedContainerColor = Color(0xFFE94560),
                            selectedLabelColor = Color.White,
                            containerColor = Color(0xFF2A2A3E),
                            labelColor = Color.Gray
                        )
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Records List
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(filteredRecords) { record ->
                    AttendanceRecordCard(record = record)
                }
            }
        }
    }
}

@Composable
fun HistoryStat(value: String, label: String, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

@Composable
fun AttendanceRecordCard(record: AttendanceRecord) {
    val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    NeonCard(
        modifier = Modifier.fillMaxWidth(),
        glowColor = record.method.color
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Method Icon
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                record.method.color.copy(alpha = 0.3f),
                                record.method.color.copy(alpha = 0.1f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = record.method.icon,
                    contentDescription = record.method.displayName,
                    tint = record.method.color,
                    modifier = Modifier.size(24.dp)
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Record Details
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = record.method.displayName,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    
                    // Status Badge
                    Box(
                        modifier = Modifier
                            .clip(RoundedCornerShape(12.dp))
                            .background(record.status.color.copy(alpha = 0.2f))
                            .padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = record.status.displayName,
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Bold,
                            color = record.status.color
                        )
                    }
                }
                
                Text(
                    text = record.location,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 2.dp)
                )
                
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "${dateFormat.format(record.timestamp)} • ${timeFormat.format(record.timestamp)}",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    
                    record.duration?.let { duration ->
                        Text(
                            text = "Duration: $duration",
                            fontSize = 12.sp,
                            color = Color(0xFF4CAF50)
                        )
                    }
                }
            }
        }
    }
}

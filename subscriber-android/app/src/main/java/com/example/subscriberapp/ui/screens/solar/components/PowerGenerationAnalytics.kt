package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime import - using String for dates
import kotlin.math.*

@Composable
fun PowerGenerationAnalytics(
    generationData: PowerGenerationData,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    var selectedTimeRange by remember { mutableStateOf(TimeRange.DAY) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Power Generation Analytics",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFFE94560))
            }
        } else {
            // Time Range Selector
            TimeRangeSelector(
                selectedRange = selectedTimeRange,
                onRangeSelected = { selectedTimeRange = it }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Generation Summary Cards
            GenerationSummaryCards(generationData = generationData)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Advanced Interactive Chart
            val chartData = when (selectedTimeRange) {
                TimeRange.DAY -> generationData.todayGeneration.map { hourlyData ->
                    GenerationDataPoint(
                        timestamp = "${hourlyData.hour}:00", // Simplified timestamp
                        powerGenerated = hourlyData.powerGenerated,
                        efficiency = hourlyData.efficiency,
                        weatherCondition = hourlyData.weatherCondition
                    )
                }
                TimeRange.MONTH -> generationData.monthlyGeneration.map { dailyData ->
                    GenerationDataPoint(
                        timestamp = dailyData.date, // dailyData.date is already a String
                        powerGenerated = dailyData.totalGenerated,
                        efficiency = dailyData.averageEfficiency,
                        weatherCondition = dailyData.weatherCondition
                    )
                }
                else -> emptyList()
            }

            if (chartData.isNotEmpty()) {
                InteractiveGenerationChart(
                    generationData = chartData,
                    chartType = ChartType.LINE,
                    timeRange = selectedTimeRange,
                    onDataPointSelected = { dataPoint ->
                        // Handle data point selection
                    },
                    onExportData = {
                        // Handle export request
                    },
                    modifier = Modifier.fillMaxWidth()
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                        .background(
                            Color.Black.copy(alpha = 0.3f),
                            RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Chart for ${selectedTimeRange.name} coming soon",
                        color = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Weather Impact and Efficiency
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                WeatherImpactCard(
                    weatherImpact = generationData.weatherImpact,
                    modifier = Modifier.weight(1f)
                )
                
                EfficiencyCard(
                    efficiency = generationData.averageEfficiency,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Composable
private fun TimeRangeSelector(
    selectedRange: TimeRange,
    onRangeSelected: (TimeRange) -> Unit
) {
    val timeRanges = listOf(
        TimeRange.DAY to "Today",
        TimeRange.WEEK to "Week",
        TimeRange.MONTH to "Month",
        TimeRange.YEAR to "Year"
    )
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(timeRanges) { (range, label) ->
            FilterChip(
                onClick = { onRangeSelected(range) },
                label = { Text(label) },
                selected = selectedRange == range,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = Color(0xFFE94560),
                    selectedLabelColor = Color.White,
                    containerColor = Color.Black.copy(alpha = 0.3f),
                    labelColor = Color.Gray
                )
            )
        }
    }
}

@Composable
private fun GenerationSummaryCards(
    generationData: PowerGenerationData
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        SummaryCard(
            title = "Today",
            value = "${String.format("%.1f", generationData.totalGeneratedToday)} kWh",
            icon = Icons.Default.Today,
            color = Color(0xFF4CAF50),
            modifier = Modifier.weight(1f)
        )
        
        SummaryCard(
            title = "This Month",
            value = "${String.format("%.0f", generationData.totalGeneratedThisMonth)} kWh",
            icon = Icons.Default.CalendarMonth,
            color = Color(0xFF2196F3),
            modifier = Modifier.weight(1f)
        )
        
        SummaryCard(
            title = "Peak Today",
            value = "${String.format("%.1f", generationData.peakGenerationToday)} kW",
            icon = Icons.Default.TrendingUp,
            color = Color(0xFFFF9800),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun SummaryCard(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = title,
                fontSize = 10.sp,
                color = Color.Gray
            )
            
            Text(
                text = value,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        }
    }
}

@Composable
private fun HourlyGenerationChart(
    hourlyData: List<HourlyGenerationData>,
    modifier: Modifier = Modifier
) {
    val animatedProgress by animateFloatAsState(
        targetValue = 1f,
        animationSpec = tween(1500, easing = FastOutSlowInEasing),
        label = "chart_animation"
    )
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Hourly Generation (Today)",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawHourlyChart(hourlyData, animatedProgress)
            }
        }
    }
}

private fun DrawScope.drawHourlyChart(
    hourlyData: List<HourlyGenerationData>,
    animatedProgress: Float
) {
    if (hourlyData.isEmpty()) return
    
    val chartWidth = size.width - 40.dp.toPx()
    val chartHeight = size.height - 40.dp.toPx()
    val chartLeft = 20.dp.toPx()
    val chartTop = 20.dp.toPx()
    
    val maxGeneration = hourlyData.maxOfOrNull { it.powerGenerated } ?: 1f
    val barWidth = chartWidth / hourlyData.size
    
    // Draw bars
    hourlyData.forEachIndexed { index, data ->
        val barHeight = (data.powerGenerated / maxGeneration) * chartHeight * animatedProgress
        val barLeft = chartLeft + index * barWidth + barWidth * 0.1f
        val barTop = chartTop + chartHeight - barHeight
        val barRight = barLeft + barWidth * 0.8f
        
        val barColor = when {
            data.powerGenerated > maxGeneration * 0.8f -> Color(0xFF4CAF50)
            data.powerGenerated > maxGeneration * 0.5f -> Color(0xFFFF9800)
            data.powerGenerated > 0f -> Color(0xFFFFEB3B)
            else -> Color.Gray
        }
        
        drawRect(
            color = barColor,
            topLeft = Offset(barLeft, barTop),
            size = Size(barRight - barLeft, barHeight)
        )
        
        // Draw hour labels for every 4th hour
        if (index % 4 == 0) {
            drawContext.canvas.nativeCanvas.drawText(
                "${data.hour}:00",
                barLeft + (barRight - barLeft) / 2,
                chartTop + chartHeight + 15.dp.toPx(),
                android.graphics.Paint().apply {
                    color = android.graphics.Color.GRAY
                    textSize = 10.sp.toPx()
                    textAlign = android.graphics.Paint.Align.CENTER
                }
            )
        }
    }
    
    // Draw grid lines
    for (i in 0..4) {
        val y = chartTop + (chartHeight * i / 4)
        drawLine(
            color = Color.Gray.copy(alpha = 0.3f),
            start = Offset(chartLeft, y),
            end = Offset(chartLeft + chartWidth, y),
            strokeWidth = 1.dp.toPx()
        )
        
        // Y-axis labels
        val value = maxGeneration * (4 - i) / 4
        drawContext.canvas.nativeCanvas.drawText(
            String.format("%.1f", value),
            chartLeft - 5.dp.toPx(),
            y + 4.dp.toPx(),
            android.graphics.Paint().apply {
                color = android.graphics.Color.GRAY
                textSize = 10.sp.toPx()
                textAlign = android.graphics.Paint.Align.RIGHT
            }
        )
    }
}

@Composable
private fun DailyGenerationChart(
    dailyData: List<DailyGenerationData>,
    modifier: Modifier = Modifier
) {
    val animatedProgress by animateFloatAsState(
        targetValue = 1f,
        animationSpec = tween(2000, easing = FastOutSlowInEasing),
        label = "daily_chart_animation"
    )
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Daily Generation (Last 30 Days)",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawDailyChart(dailyData, animatedProgress)
            }
        }
    }
}

private fun DrawScope.drawDailyChart(
    dailyData: List<DailyGenerationData>,
    animatedProgress: Float
) {
    if (dailyData.isEmpty()) return
    
    val chartWidth = size.width - 40.dp.toPx()
    val chartHeight = size.height - 40.dp.toPx()
    val chartLeft = 20.dp.toPx()
    val chartTop = 20.dp.toPx()
    
    val maxGeneration = dailyData.maxOfOrNull { it.totalGenerated } ?: 1f
    val pointSpacing = chartWidth / (dailyData.size - 1)
    
    // Create path for line chart
    val path = Path()
    val points = mutableListOf<Offset>()
    
    dailyData.forEachIndexed { index, data ->
        val x = chartLeft + index * pointSpacing
        val y = chartTop + chartHeight - (data.totalGenerated / maxGeneration) * chartHeight * animatedProgress
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // Draw line
    drawPath(
        path = path,
        color = Color(0xFF4CAF50),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // Draw points
    points.forEach { point ->
        drawCircle(
            color = Color(0xFF4CAF50),
            radius = 4.dp.toPx(),
            center = point
        )
        drawCircle(
            color = Color.White,
            radius = 2.dp.toPx(),
            center = point
        )
    }
    
    // Draw grid lines
    for (i in 0..4) {
        val y = chartTop + (chartHeight * i / 4)
        drawLine(
            color = Color.Gray.copy(alpha = 0.3f),
            start = Offset(chartLeft, y),
            end = Offset(chartLeft + chartWidth, y),
            strokeWidth = 1.dp.toPx()
        )
    }
}

@Composable
private fun WeatherImpactCard(
    weatherImpact: WeatherImpact,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = getWeatherIcon(weatherImpact.currentCondition),
                contentDescription = "Weather",
                tint = getWeatherColor(weatherImpact.currentCondition),
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Weather Impact",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = "${weatherImpact.currentCondition.name.replace("_", " ")}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
            
            Text(
                text = "${String.format("%.0f", weatherImpact.expectedImpact)}%",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = if (weatherImpact.expectedImpact >= 0) Color(0xFF4CAF50) else Color(0xFFFF5722)
            )
        }
    }
}

@Composable
private fun EfficiencyCard(
    efficiency: Float,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Speed,
                contentDescription = "Efficiency",
                tint = getEfficiencyColor(efficiency),
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Avg Efficiency",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = "${efficiency.toInt()}%",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = getEfficiencyColor(efficiency)
            )
            
            LinearProgressIndicator(
                progress = { efficiency / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = getEfficiencyColor(efficiency),
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
        }
    }
}

private fun getWeatherIcon(condition: WeatherCondition): androidx.compose.ui.graphics.vector.ImageVector {
    return when (condition) {
        WeatherCondition.SUNNY -> Icons.Default.WbSunny
        WeatherCondition.PARTLY_CLOUDY -> Icons.Default.Cloud
        WeatherCondition.CLOUDY -> Icons.Default.CloudQueue
        WeatherCondition.RAINY -> Icons.Default.Umbrella
        WeatherCondition.STORMY -> Icons.Default.FlashOn
    }
}

private fun getWeatherColor(condition: WeatherCondition): Color {
    return when (condition) {
        WeatherCondition.SUNNY -> Color(0xFFFFD700)
        WeatherCondition.PARTLY_CLOUDY -> Color(0xFF87CEEB)
        WeatherCondition.CLOUDY -> Color(0xFF708090)
        WeatherCondition.RAINY -> Color(0xFF4682B4)
        WeatherCondition.STORMY -> Color(0xFF483D8B)
    }
}

private fun getEfficiencyColor(efficiency: Float): Color {
    return when {
        efficiency > 90f -> Color(0xFF4CAF50)
        efficiency > 80f -> Color(0xFF8BC34A)
        efficiency > 70f -> Color(0xFFFFEB3B)
        efficiency > 60f -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
}

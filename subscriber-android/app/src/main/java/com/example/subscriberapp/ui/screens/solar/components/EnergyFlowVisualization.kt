package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.EnergyFlowData
import com.example.subscriberapp.ui.screens.solar.model.EnergyRoutingMode
import kotlin.math.*

@Composable
fun EnergyFlowVisualization(
    energyFlow: EnergyFlowData,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Energy Flow",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFFE94560))
            }
        } else {
            // Main Energy Flow Diagram
            EnergyFlowDiagram(
                energyFlow = energyFlow,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(250.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Real-time Metrics
            EnergyMetricsDisplay(energyFlow = energyFlow)
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Routing Mode Indicator
            RoutingModeIndicator(routingMode = energyFlow.routingMode)
        }
    }
}

@Composable
private fun EnergyFlowDiagram(
    energyFlow: EnergyFlowData,
    modifier: Modifier = Modifier
) {
    // Animation for flow particles
    val infiniteTransition = rememberInfiniteTransition(label = "flow_animation")
    val flowOffset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "flow_offset"
    )
    
    // Pulsing animation for active components
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.9f,
        targetValue = 1.1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulse_scale"
    )
    
    Canvas(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(Color.Black.copy(alpha = 0.3f))
            .padding(16.dp)
    ) {
        val canvasWidth = size.width
        val canvasHeight = size.height
        
        // Component positions
        val solarPanelPos = Offset(canvasWidth * 0.1f, canvasHeight * 0.2f)
        val inverterPos = Offset(canvasWidth * 0.5f, canvasHeight * 0.5f)
        val batteryPos = Offset(canvasWidth * 0.8f, canvasHeight * 0.2f)
        val householdPos = Offset(canvasWidth * 0.8f, canvasHeight * 0.8f)
        val gridPos = Offset(canvasWidth * 0.2f, canvasHeight * 0.8f)
        
        // Draw components
        drawSolarPanel(solarPanelPos, energyFlow.currentGeneration, pulseScale)
        drawInverter(inverterPos, energyFlow.voltage)
        drawBattery(batteryPos, energyFlow.powerToBattery, pulseScale)
        drawHousehold(householdPos, energyFlow.powerToHousehold)
        drawGrid(gridPos, energyFlow.powerFromGrid)
        
        // Draw energy flow lines with animation
        drawEnergyFlow(
            from = solarPanelPos,
            to = inverterPos,
            power = energyFlow.currentGeneration,
            flowOffset = flowOffset,
            isActive = energyFlow.currentGeneration > 0
        )
        
        when (energyFlow.routingMode) {
            EnergyRoutingMode.HOUSEHOLD_DIRECT -> {
                drawEnergyFlow(inverterPos, householdPos, energyFlow.powerToHousehold, flowOffset, true)
            }
            EnergyRoutingMode.BATTERY_CHARGING -> {
                drawEnergyFlow(inverterPos, batteryPos, energyFlow.powerToBattery, flowOffset, true)
            }
            EnergyRoutingMode.BATTERY_DISCHARGING -> {
                drawEnergyFlow(batteryPos, householdPos, energyFlow.powerToHousehold, flowOffset, true)
            }
            EnergyRoutingMode.GRID_FEEDING -> {
                drawEnergyFlow(inverterPos, gridPos, energyFlow.powerFromGrid, flowOffset, true)
            }
            EnergyRoutingMode.HYBRID -> {
                drawEnergyFlow(inverterPos, batteryPos, energyFlow.powerToBattery, flowOffset, energyFlow.powerToBattery > 0)
                drawEnergyFlow(inverterPos, householdPos, energyFlow.powerToHousehold, flowOffset, energyFlow.powerToHousehold > 0)
                if (energyFlow.powerFromGrid > 0) {
                    drawEnergyFlow(gridPos, householdPos, energyFlow.powerFromGrid, flowOffset, true)
                }
            }
        }
    }
}

private fun DrawScope.drawSolarPanel(
    position: Offset,
    power: Float,
    pulseScale: Float
) {
    val size = 60.dp.toPx() * if (power > 0) pulseScale else 1f
    val color = when {
        power > 4f -> Color(0xFF4CAF50)
        power > 2f -> Color(0xFFFF9800)
        power > 0f -> Color(0xFFFFEB3B)
        else -> Color.Gray
    }
    
    // Solar panel rectangle
    drawRect(
        color = color,
        topLeft = Offset(position.x - size/2, position.y - size/2),
        size = Size(size, size * 0.7f),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // Solar panel grid lines
    val gridLines = 3
    for (i in 1 until gridLines) {
        val x = position.x - size/2 + (size * i / gridLines)
        drawLine(
            color = color,
            start = Offset(x, position.y - size/2),
            end = Offset(x, position.y + size/2 * 0.7f),
            strokeWidth = 1.dp.toPx()
        )
    }
}

private fun DrawScope.drawInverter(
    position: Offset,
    voltage: Float
) {
    val size = 50.dp.toPx()
    val color = if (voltage > 200f) Color(0xFF2196F3) else Color.Gray
    
    // Inverter box
    drawRect(
        color = color,
        topLeft = Offset(position.x - size/2, position.y - size/2),
        size = Size(size, size),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // AC wave symbol
    val wavePoints = mutableListOf<Offset>()
    for (i in 0..20) {
        val x = position.x - size/4 + (size/2 * i / 20)
        val y = position.y + sin(i * PI / 5) * size/8
        wavePoints.add(Offset(x.toFloat(), y.toFloat()))
    }
    
    for (i in 0 until wavePoints.size - 1) {
        drawLine(
            color = color,
            start = wavePoints[i],
            end = wavePoints[i + 1],
            strokeWidth = 2.dp.toPx()
        )
    }
}

private fun DrawScope.drawBattery(
    position: Offset,
    chargePower: Float,
    pulseScale: Float
) {
    val size = 45.dp.toPx() * if (chargePower > 0) pulseScale else 1f
    val color = when {
        chargePower > 0 -> Color(0xFF4CAF50) // Charging
        chargePower < 0 -> Color(0xFFFF5722) // Discharging
        else -> Color.Gray
    }
    
    // Battery body
    drawRect(
        color = color,
        topLeft = Offset(position.x - size/2, position.y - size/2),
        size = Size(size, size * 0.6f),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // Battery terminal
    drawRect(
        color = color,
        topLeft = Offset(position.x - size/6, position.y - size/2 - size/10),
        size = Size(size/3, size/10),
        style = Stroke(width = 2.dp.toPx())
    )
    
    // Charge level indicator
    val chargeLevel = 0.7f // This would come from actual battery data
    drawRect(
        color = color.copy(alpha = 0.7f),
        topLeft = Offset(position.x - size/2 + 3.dp.toPx(), position.y - size/2 + 3.dp.toPx()),
        size = Size((size - 6.dp.toPx()) * chargeLevel, size * 0.6f - 6.dp.toPx())
    )
}

private fun DrawScope.drawHousehold(
    position: Offset,
    power: Float
) {
    val size = 50.dp.toPx()
    val color = if (power > 0) Color(0xFFFF9800) else Color.Gray
    
    // House base
    drawRect(
        color = color,
        topLeft = Offset(position.x - size/2, position.y - size/4),
        size = Size(size, size/2),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // House roof
    val roofPath = Path().apply {
        moveTo(position.x - size/2, position.y - size/4)
        lineTo(position.x, position.y - size/2)
        lineTo(position.x + size/2, position.y - size/4)
        close()
    }
    drawPath(
        path = roofPath,
        color = color,
        style = Stroke(width = 3.dp.toPx())
    )
}

private fun DrawScope.drawGrid(
    position: Offset,
    power: Float
) {
    val size = 40.dp.toPx()
    val color = if (abs(power) > 0) Color(0xFF9C27B0) else Color.Gray
    
    // Grid symbol (electrical tower)
    drawLine(
        color = color,
        start = Offset(position.x, position.y - size/2),
        end = Offset(position.x, position.y + size/2),
        strokeWidth = 4.dp.toPx()
    )
    
    // Cross arms
    drawLine(
        color = color,
        start = Offset(position.x - size/3, position.y - size/4),
        end = Offset(position.x + size/3, position.y - size/4),
        strokeWidth = 3.dp.toPx()
    )
    
    drawLine(
        color = color,
        start = Offset(position.x - size/4, position.y),
        end = Offset(position.x + size/4, position.y),
        strokeWidth = 3.dp.toPx()
    )
}

private fun DrawScope.drawEnergyFlow(
    from: Offset,
    to: Offset,
    power: Float,
    flowOffset: Float,
    isActive: Boolean
) {
    if (!isActive || power <= 0) return
    
    val flowColor = when {
        power > 3f -> Color(0xFF4CAF50)
        power > 1f -> Color(0xFFFF9800)
        else -> Color(0xFFFFEB3B)
    }
    
    // Draw connection line
    drawLine(
        color = flowColor.copy(alpha = 0.6f),
        start = from,
        end = to,
        strokeWidth = 4.dp.toPx()
    )
    
    // Draw animated flow particles
    val distance = sqrt((to.x - from.x).pow(2) + (to.y - from.y).pow(2))
    val direction = Offset((to.x - from.x) / distance, (to.y - from.y) / distance)
    
    for (i in 0..3) {
        val particleProgress = (flowOffset + i * 0.25f) % 1f
        val particlePos = Offset(
            from.x + direction.x * distance * particleProgress,
            from.y + direction.y * distance * particleProgress
        )
        
        drawCircle(
            color = flowColor,
            radius = 4.dp.toPx(),
            center = particlePos
        )
    }
    
    // Draw arrow at the end
    val arrowSize = 8.dp.toPx()
    val arrowAngle = atan2(to.y - from.y, to.x - from.x)
    val arrowTip = Offset(
        to.x - direction.x * 20.dp.toPx(),
        to.y - direction.y * 20.dp.toPx()
    )
    
    val arrowPath = Path().apply {
        moveTo(arrowTip.x, arrowTip.y)
        lineTo(
            arrowTip.x - arrowSize * cos(arrowAngle - PI/6).toFloat(),
            arrowTip.y - arrowSize * sin(arrowAngle - PI/6).toFloat()
        )
        moveTo(arrowTip.x, arrowTip.y)
        lineTo(
            arrowTip.x - arrowSize * cos(arrowAngle + PI/6).toFloat(),
            arrowTip.y - arrowSize * sin(arrowAngle + PI/6).toFloat()
        )
    }
    
    drawPath(
        path = arrowPath,
        color = flowColor,
        style = Stroke(width = 3.dp.toPx())
    )
}

@Composable
private fun EnergyMetricsDisplay(
    energyFlow: EnergyFlowData
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        MetricCard(
            label = "Generation",
            value = "${String.format("%.1f", energyFlow.currentGeneration)} kW",
            color = Color(0xFF4CAF50)
        )
        
        MetricCard(
            label = "Voltage",
            value = "${String.format("%.0f", energyFlow.voltage)} V",
            color = Color(0xFF2196F3)
        )
        
        MetricCard(
            label = "Current",
            value = "${String.format("%.1f", energyFlow.current)} A",
            color = Color(0xFFFF9800)
        )
    }
}

@Composable
private fun MetricCard(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .background(
                color = color.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp)
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        Text(
            text = value,
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
private fun RoutingModeIndicator(
    routingMode: EnergyRoutingMode
) {
    val (modeText, modeColor) = when (routingMode) {
        EnergyRoutingMode.HOUSEHOLD_DIRECT -> "Direct to Household" to Color(0xFF4CAF50)
        EnergyRoutingMode.BATTERY_CHARGING -> "Charging Battery" to Color(0xFF2196F3)
        EnergyRoutingMode.BATTERY_DISCHARGING -> "Battery Power" to Color(0xFFFF5722)
        EnergyRoutingMode.GRID_FEEDING -> "Feeding Grid" to Color(0xFF9C27B0)
        EnergyRoutingMode.HYBRID -> "Hybrid Mode" to Color(0xFFFF9800)
    }
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .fillMaxWidth()
            .background(
                color = modeColor.copy(alpha = 0.1f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(12.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color = modeColor, shape = CircleShape)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Text(
            text = "Current Mode: $modeText",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White
        )
    }
}

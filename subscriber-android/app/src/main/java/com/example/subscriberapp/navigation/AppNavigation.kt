package com.example.subscriberapp.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.example.subscriberapp.ui.screens.login.LoginScreen
import com.example.subscriberapp.ui.screens.dashboard.DashboardScreen
import com.example.subscriberapp.ui.screens.qr.QRScanScreen
import com.example.subscriberapp.ui.screens.qr.QRSuccessScreen
import com.example.subscriberapp.ui.screens.qr.QRErrorScreen
import com.example.subscriberapp.ui.screens.nfc.NFCScreen
import com.example.subscriberapp.ui.screens.wifi.WiFiScreen
import com.example.subscriberapp.ui.screens.bluetooth.BluetoothScreen
import com.example.subscriberapp.ui.screens.iot.IOTDashboardScreen
import com.example.subscriberapp.ui.screens.iot.SmartLightingScreen
import com.example.subscriberapp.ui.screens.profile.ProfileScreen
import com.example.subscriberapp.ui.screens.history.HistoryScreen
import com.example.subscriberapp.ui.screens.solar.SolarPanelMonitoringScreen
import com.example.subscriberapp.services.MockAuthService

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController(),
    authService: MockAuthService = viewModel()
) {
    val isLoggedIn by authService.isLoggedIn.collectAsState()
    
    NavHost(
        navController = navController,
        startDestination = if (isLoggedIn) "dashboard" else "login"
    ) {
        composable("login") {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate("dashboard") {
                        popUpTo("login") { inclusive = true }
                    }
                }
            )
        }
        
        composable("dashboard") {
            DashboardScreen(
                onNavigateToQR = { navController.navigate("qr_scan") },
                onNavigateToNFC = { navController.navigate("nfc") },
                onNavigateToWiFi = { navController.navigate("wifi") },
                onNavigateToBluetooth = { navController.navigate("bluetooth") },
                onNavigateToIOT = { navController.navigate("iot_dashboard") },
                onNavigateToSolar = { navController.navigate("solar_monitoring") },
                onNavigateToProfile = { navController.navigate("profile") },
                onNavigateToHistory = { navController.navigate("history") }
            )
        }
        
        composable("qr_scan") {
            QRScanScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToSuccess = { navController.navigate("qr_success") },
                onNavigateToError = { navController.navigate("qr_error") }
            )
        }

        composable("qr_success") {
            QRSuccessScreen(
                onNavigateBack = {
                    navController.popBackStack("qr_scan", inclusive = false)
                },
                onScanAnother = {
                    navController.popBackStack("qr_scan", inclusive = false)
                }
            )
        }

        composable("qr_error") {
            QRErrorScreen(
                onNavigateBack = {
                    navController.popBackStack("qr_scan", inclusive = false)
                },
                onTryAgain = {
                    navController.popBackStack("qr_scan", inclusive = false)
                }
            )
        }
        
        composable("nfc") {
            NFCScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("wifi") {
            WiFiScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("bluetooth") {
            BluetoothScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("iot_dashboard") {
            IOTDashboardScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToLighting = { navController.navigate("smart_lighting") }
            )
        }

        composable("smart_lighting") {
            SmartLightingScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("profile") {
            ProfileScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        
        composable("history") {
            HistoryScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable("solar_monitoring") {
            SolarPanelMonitoringScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
    }
}

package com.example.subscriberapp.ui.screens.iot

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.components.NeonCard

data class IOTDevice(
    val name: String,
    val category: String,
    val icon: ImageVector,
    val colors: List<Color>,
    val isOnline: Boolean = true,
    val status: String = "Online",
    val onClick: () -> Unit = {}
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun IOTDashboardScreen(
    onNavigateBack: () -> Unit,
    onNavigateToLighting: () -> Unit = {}
) {
    var selectedCategory by remember { mutableStateOf("All") }
    
    val iotDevices = listOf(
        IOTDevice(
            name = "Smart Lighting",
            category = "Lighting",
            icon = Icons.Default.Lightbulb,
            colors = listOf(Color(0xFFFFD700), Color(0xFFFFA500)),
            status = "12 lights connected",
            onClick = onNavigateToLighting
        ),
        IOTDevice(
            name = "Climate Control",
            category = "Climate",
            icon = Icons.Default.Thermostat,
            colors = listOf(Color(0xFF00BCD4), Color(0xFF0097A7)),
            status = "22°C • Auto mode"
        ),
        IOTDevice(
            name = "Security System",
            category = "Security",
            icon = Icons.Default.Security,
            colors = listOf(Color(0xFFF44336), Color(0xFFD32F2F)),
            status = "Armed • 8 sensors"
        ),
        IOTDevice(
            name = "Entertainment",
            category = "Media",
            icon = Icons.Default.Tv,
            colors = listOf(Color(0xFF9C27B0), Color(0xFF7B1FA2)),
            status = "Living room active"
        ),
        IOTDevice(
            name = "Kitchen Hub",
            category = "Kitchen",
            icon = Icons.Default.Kitchen,
            colors = listOf(Color(0xFF4CAF50), Color(0xFF388E3C)),
            status = "3 appliances online"
        ),
        IOTDevice(
            name = "Garden Control",
            category = "Garden",
            icon = Icons.Default.Grass,
            colors = listOf(Color(0xFF8BC34A), Color(0xFF689F38)),
            status = "Sprinklers scheduled"
        ),
        IOTDevice(
            name = "Energy Monitor",
            category = "Energy",
            icon = Icons.Default.BatteryChargingFull,
            colors = listOf(Color(0xFF03DAC6), Color(0xFF018786)),
            status = "Solar: 85% efficiency"
        ),
        IOTDevice(
            name = "Automation",
            category = "Automation",
            icon = Icons.Default.AutoMode,
            colors = listOf(Color(0xFF673AB7), Color(0xFF512DA8)),
            status = "5 scenes active"
        )
    )
    
    val categories = listOf("All", "Lighting", "Climate", "Security", "Media", "Kitchen", "Garden", "Energy", "Automation")
    
    val filteredDevices = if (selectedCategory == "All") {
        iotDevices
    } else {
        iotDevices.filter { it.category == selectedCategory }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Smart Home Control",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Status Overview
            NeonCard(
                modifier = Modifier.fillMaxWidth(),
                glowColor = Color(0xFF00BCD4)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatusItem("24", "Devices", Color(0xFF4CAF50))
                    StatusItem("22", "Online", Color(0xFF00BCD4))
                    StatusItem("2", "Offline", Color(0xFFFF9800))
                    StatusItem("5", "Scenes", Color(0xFF9C27B0))
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Category Filter
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.height(120.dp)
            ) {
                items(categories) { category ->
                    CategoryChip(
                        category = category,
                        isSelected = category == selectedCategory,
                        onClick = { selectedCategory = category }
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Device Grid
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(filteredDevices) { device ->
                    IOTDeviceCard(device = device)
                }
            }
        }
    }
}

@Composable
fun StatusItem(value: String, label: String, color: Color) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

@Composable
fun CategoryChip(
    category: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .height(40.dp)
            .clip(RoundedCornerShape(20.dp))
            .background(
                if (isSelected) {
                    Brush.horizontalGradient(
                        colors = listOf(Color(0xFFE94560), Color(0xFFFF6B9D))
                    )
                } else {
                    Brush.horizontalGradient(
                        colors = listOf(Color(0xFF2A2A3E), Color(0xFF1A1A2E))
                    )
                }
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = category,
            color = Color.White,
            fontSize = 12.sp,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun IOTDeviceCard(device: IOTDevice) {
    NeonCard(
        modifier = Modifier
            .aspectRatio(1f)
            .clickable { device.onClick() },
        glowColor = device.colors.first()
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = device.colors.map { it.copy(alpha = 0.3f) }
                    )
                )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = device.icon,
                    contentDescription = device.name,
                    tint = device.colors.first(),
                    modifier = Modifier.size(48.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = device.name,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = device.status,
                    color = Color.Gray,
                    fontSize = 12.sp,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                // Online indicator
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(top = 8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                if (device.isOnline) Color(0xFF4CAF50) else Color(0xFFF44336),
                                RoundedCornerShape(4.dp)
                            )
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = if (device.isOnline) "Online" else "Offline",
                        color = if (device.isOnline) Color(0xFF4CAF50) else Color(0xFFF44336),
                        fontSize = 10.sp
                    )
                }
            }
        }
    }
}

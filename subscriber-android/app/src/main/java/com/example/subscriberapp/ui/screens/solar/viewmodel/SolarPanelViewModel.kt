package com.example.subscriberapp.ui.screens.solar.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.subscriberapp.ui.screens.solar.model.*
import com.example.subscriberapp.ui.screens.solar.repository.SolarPanelRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class SolarPanelViewModel(
    private val repository: SolarPanelRepository = SolarPanelRepository()
) : ViewModel() {

    private val _uiState = MutableStateFlow(SolarPanelUiState(isLoading = true))
    val uiState: StateFlow<SolarPanelUiState> = _uiState.asStateFlow()

    init {
        loadInitialData()
        startRealTimeUpdates()
    }

    private fun loadInitialData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                // Load all data concurrently for better performance
                val systemStatus = repository.getSystemStatus()
                val energyFlow = repository.getEnergyFlowData()
                val batteryStatus = repository.getBatteryStatus()
                val generationData = repository.getPowerGenerationData()
                val healthStatus = repository.getSystemHealthStatus()
                val maintenanceData = repository.getMaintenanceData()
                // Phase 3: Load enhanced analytics data
                val historicalData = repository.getHistoricalData(TimeRange.MONTH)
                val performanceMetrics = repository.getPerformanceMetrics(PerformanceMetricType.GENERATION)

                _uiState.value = SolarPanelUiState(
                    isLoading = false,
                    systemStatus = systemStatus,
                    energyFlow = energyFlow,
                    batteryStatus = batteryStatus,
                    generationData = generationData,
                    healthStatus = healthStatus,
                    maintenanceData = maintenanceData,
                    historicalData = historicalData,
                    performanceMetrics = performanceMetrics,
                    selectedTimeRange = TimeRange.DAY,
                    selectedPerformanceMetric = PerformanceMetricType.GENERATION
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load solar panel data. Please check your connection and try again."
                )
                // Log the actual error for debugging
                println("SolarPanelViewModel Error: ${e.message}")
            }
        }
    }

    private fun startRealTimeUpdates() {
        viewModelScope.launch {
            // Update real-time data every 5 seconds
            repository.getRealTimeUpdates()
                .collect { updates ->
                    _uiState.value = _uiState.value.copy(
                        systemStatus = updates.systemStatus,
                        energyFlow = updates.energyFlow,
                        batteryStatus = updates.batteryStatus
                    )
                }
        }
    }

    // refreshData method moved to end of class

    fun updateTimeRange(timeRange: TimeRange) {
        viewModelScope.launch {
            try {
                val generationData = repository.getPowerGenerationData(timeRange)
                val historicalData = repository.getHistoricalData(timeRange)
                _uiState.value = _uiState.value.copy(
                    generationData = generationData,
                    historicalData = historicalData,
                    selectedTimeRange = timeRange
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update generation data: ${e.message}"
                )
            }
        }
    }

    fun updateSelectedMetric(metricType: PerformanceMetricType) {
        viewModelScope.launch {
            try {
                val performanceMetrics = repository.getPerformanceMetrics(metricType)
                _uiState.value = _uiState.value.copy(
                    performanceMetrics = performanceMetrics,
                    selectedPerformanceMetric = metricType
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load performance metrics: ${e.message}"
                )
            }
        }
    }

    fun acknowledgeAlert(alertId: String) {
        viewModelScope.launch {
            try {
                repository.acknowledgeAlert(alertId)
                val updatedHealthStatus = repository.getSystemHealthStatus()
                _uiState.value = _uiState.value.copy(
                    healthStatus = updatedHealthStatus
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to acknowledge alert: ${e.message}"
                )
            }
        }
    }

    fun completeMaintenanceTask(taskId: String) {
        viewModelScope.launch {
            try {
                repository.completeMaintenanceTask(taskId)
                val updatedMaintenanceData = repository.getMaintenanceData()
                _uiState.value = _uiState.value.copy(
                    maintenanceData = updatedMaintenanceData
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to complete maintenance task: ${e.message}"
                )
            }
        }
    }

    fun scheduleMaintenanceTask(task: MaintenanceTask) {
        viewModelScope.launch {
            try {
                repository.scheduleMaintenanceTask(task)
                val updatedMaintenanceData = repository.getMaintenanceData()
                _uiState.value = _uiState.value.copy(
                    maintenanceData = updatedMaintenanceData
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to schedule maintenance task: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun loadHistoricalData(timeRange: TimeRange) {
        viewModelScope.launch {
            try {
                val historicalData = repository.getHistoricalData(timeRange)
                // Update UI state with historical data if needed
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load historical data: ${e.message}"
                )
            }
        }
    }

    fun loadPerformanceMetrics(metricType: com.example.subscriberapp.ui.screens.solar.components.PerformanceMetricType) {
        viewModelScope.launch {
            try {
                val performanceMetrics = repository.getPerformanceMetrics(metricType)
                // Update UI state with performance metrics if needed
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to load performance metrics: ${e.message}"
                )
            }
        }
    }

    fun exportData(format: ExportFormat, timeRange: TimeRange) {
        viewModelScope.launch {
            try {
                // Start export process
                _uiState.value = _uiState.value.copy(
                    exportStatus = ExportStatus(
                        isExporting = true,
                        format = format,
                        progress = 0f
                    )
                )

                val result = repository.exportData(format, timeRange)

                // Complete export
                _uiState.value = _uiState.value.copy(
                    exportStatus = ExportStatus(
                        isExporting = false,
                        format = format,
                        progress = 100f,
                        filePath = result
                    )
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    exportStatus = ExportStatus(
                        isExporting = false,
                        format = format,
                        error = e.message
                    ),
                    error = "Failed to export data: ${e.message}"
                )
            }
        }
    }

    fun refreshData() {
        _uiState.value = _uiState.value.copy(error = null)
        loadInitialData()
    }

    override fun onCleared() {
        super.onCleared()
        // Clean up any resources if needed
    }
}

/**
 * Real-time update data class
 */
data class RealTimeUpdates(
    val systemStatus: SystemStatus,
    val energyFlow: EnergyFlowData,
    val batteryStatus: BatteryStatus,
    val timestamp: String = getCurrentTimeString()
)

private fun getCurrentTimeString(): String {
    val formatter = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
    return formatter.format(java.util.Date())
}

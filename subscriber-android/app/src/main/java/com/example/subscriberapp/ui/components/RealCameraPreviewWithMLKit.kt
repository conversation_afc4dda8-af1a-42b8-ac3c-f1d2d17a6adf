package com.example.subscriberapp.ui.components

import android.content.Context
import android.util.Log
import android.view.ViewGroup
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * Real Camera Preview with ML Kit Integration
 * Provides live camera feed with QR code detection capabilities
 */
@Composable
fun RealCameraPreviewWithMLKit(
    modifier: Modifier = Modifier,
    onQRCodeDetected: (String) -> Unit,
    onError: (String) -> Unit = {},
    isScanning: Boolean = true
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    
    var cameraProvider by remember { mutableStateOf<ProcessCameraProvider?>(null) }
    var preview by remember { mutableStateOf<Preview?>(null) }
    var imageAnalyzer by remember { mutableStateOf<ImageAnalysis?>(null) }
    var camera by remember { mutableStateOf<Camera?>(null) }
    
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    
    // QR Code Analyzer
    val qrAnalyzer = remember {
        FineTunedQRCodeAnalyzer(
            onQRCodeDetected = onQRCodeDetected,
            onError = { exception ->
                Log.e("CameraPreview", "QR Analysis error", exception)
                onError("QR code analysis failed: ${exception.message}")
            }
        )
    }
    
    DisposableEffect(Unit) {
        onDispose {
            cameraExecutor.shutdown()
            qrAnalyzer.cleanup()
        }
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(16.dp))
            .background(Color.Black)
    ) {
        // Debug log
        LaunchedEffect(Unit) {
            Log.d("CameraPreview", "RealCameraPreviewWithMLKit composable started")
        }
        AndroidView(
            factory = { ctx ->
                PreviewView(ctx).apply {
                    this.scaleType = PreviewView.ScaleType.FILL_CENTER
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    
                    // Initialize camera
                    setupCamera(
                        context = ctx,
                        lifecycleOwner = lifecycleOwner,
                        previewView = this,
                        cameraExecutor = cameraExecutor,
                        qrAnalyzer = qrAnalyzer,
                        isScanning = isScanning,
                        onCameraReady = { provider, previewUseCase, analysisUseCase, cam ->
                            cameraProvider = provider
                            preview = previewUseCase
                            imageAnalyzer = analysisUseCase
                            camera = cam
                        },
                        onError = onError
                    )
                }
            },
            modifier = Modifier.fillMaxSize()
        )
        
        // Scanning overlay
        if (isScanning) {
            ScanningOverlay(
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * Scanning overlay with viewfinder frame
 */
@Composable
private fun ScanningOverlay(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        // Viewfinder frame
        Box(
            modifier = Modifier
                .size(250.dp)
                .border(
                    width = 2.dp,
                    color = Color(0xFFE94560),
                    shape = RoundedCornerShape(16.dp)
                )
        ) {
            // Corner indicators
            val cornerSize = 20.dp
            val cornerThickness = 4.dp
            val cornerColor = Color(0xFFE94560)
            
            // Top-left corner
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .size(cornerSize)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(cornerThickness)
                        .background(cornerColor)
                )
                Box(
                    modifier = Modifier
                        .width(cornerThickness)
                        .fillMaxHeight()
                        .background(cornerColor)
                )
            }
            
            // Top-right corner
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(cornerSize)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(cornerThickness)
                        .background(cornerColor)
                )
                Box(
                    modifier = Modifier
                        .width(cornerThickness)
                        .fillMaxHeight()
                        .align(Alignment.TopEnd)
                        .background(cornerColor)
                )
            }
            
            // Bottom-left corner
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(cornerSize)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(cornerThickness)
                        .align(Alignment.BottomStart)
                        .background(cornerColor)
                )
                Box(
                    modifier = Modifier
                        .width(cornerThickness)
                        .fillMaxHeight()
                        .background(cornerColor)
                )
            }
            
            // Bottom-right corner
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerSize)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(cornerThickness)
                        .align(Alignment.BottomEnd)
                        .background(cornerColor)
                )
                Box(
                    modifier = Modifier
                        .width(cornerThickness)
                        .fillMaxHeight()
                        .align(Alignment.BottomEnd)
                        .background(cornerColor)
                )
            }
        }
    }
}

/**
 * Setup camera with preview and analysis use cases
 */
private fun setupCamera(
    context: Context,
    lifecycleOwner: LifecycleOwner,
    previewView: PreviewView,
    cameraExecutor: ExecutorService,
    qrAnalyzer: FineTunedQRCodeAnalyzer,
    isScanning: Boolean,
    onCameraReady: (ProcessCameraProvider, Preview, ImageAnalysis, Camera) -> Unit,
    onError: (String) -> Unit
) {
    Log.d("CameraPreview", "setupCamera called, isScanning: $isScanning")
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
    
    cameraProviderFuture.addListener({
        try {
            val cameraProvider = cameraProviderFuture.get()
            
            // Preview use case
            val preview = Preview.Builder()
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }
            
            // Image analysis use case for QR detection
            val imageAnalyzer = ImageAnalysis.Builder()
                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                .build()
                .also {
                    if (isScanning) {
                        it.setAnalyzer(cameraExecutor, qrAnalyzer)
                    }
                }
            
            // Camera selector (prefer back camera)
            val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
            
            try {
                // Unbind all use cases before rebinding
                cameraProvider.unbindAll()
                
                // Bind use cases to camera
                val camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageAnalyzer
                )
                
                onCameraReady(cameraProvider, preview, imageAnalyzer, camera)
                
            } catch (exc: Exception) {
                Log.e("CameraPreview", "Use case binding failed", exc)
                onError("Camera binding failed: ${exc.message}")
            }
            
        } catch (exc: Exception) {
            Log.e("CameraPreview", "Camera initialization failed", exc)
            onError("Camera initialization failed: ${exc.message}")
        }
    }, ContextCompat.getMainExecutor(context))
}

package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime imports - using String for dates
import kotlin.math.abs

@Composable
fun HistoricalDataAnalysis(
    historicalData: List<HistoricalDataSet>,
    selectedTimeRange: TimeRange,
    onTimeRangeChanged: (TimeRange) -> Unit,
    onExportData: (ExportFormat, TimeRange) -> Unit,
    exportStatus: ExportStatus? = null,
    modifier: Modifier = Modifier
) {
    var selectedDataSet by remember { mutableStateOf<HistoricalDataSet?>(null) }
    var showExportDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Historical Data Analysis",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Time Range Selector
        HistoricalTimeRangeSelector(
            selectedRange = selectedTimeRange,
            onRangeSelected = onTimeRangeChanged
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Data Overview Cards
        HistoricalDataOverview(
            historicalData = historicalData,
            timeRange = selectedTimeRange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Trend Analysis
        TrendAnalysisSection(
            historicalData = historicalData,
            timeRange = selectedTimeRange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Performance Comparison
        PerformanceComparisonSection(
            historicalData = historicalData,
            onDataSetSelected = { selectedDataSet = it }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Export Controls
        ExportControlsSection(
            onExportRequested = { showExportDialog = true }
        )
        
        // Advanced Export Dialog
        if (showExportDialog) {
            AdvancedExportDialog(
                timeRange = selectedTimeRange,
                exportStatus = exportStatus,
                onExport = { format ->
                    onExportData(format, selectedTimeRange)
                    // Don't close dialog immediately - let export status control it
                },
                onDismiss = { showExportDialog = false }
            )
        }
        
        // Selected Data Set Details
        selectedDataSet?.let { dataSet ->
            Spacer(modifier = Modifier.height(16.dp))
            DataSetDetailsCard(
                dataSet = dataSet,
                onDismiss = { selectedDataSet = null }
            )
        }
    }
}

@Composable
private fun HistoricalTimeRangeSelector(
    selectedRange: TimeRange,
    onRangeSelected: (TimeRange) -> Unit
) {
    val timeRanges = listOf(
        TimeRange.WEEK to "Last Week",
        TimeRange.MONTH to "Last Month",
        TimeRange.YEAR to "Last Year",
        TimeRange.ALL_TIME to "All Time"
    )
    
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(timeRanges) { (range, label) ->
            FilterChip(
                onClick = { onRangeSelected(range) },
                label = { Text(label) },
                selected = selectedRange == range,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = Color(0xFFE94560),
                    selectedLabelColor = Color.White,
                    containerColor = Color.Black.copy(alpha = 0.3f),
                    labelColor = Color.Gray
                )
            )
        }
    }
}

@Composable
private fun HistoricalDataOverview(
    historicalData: List<HistoricalDataSet>,
    timeRange: TimeRange
) {
    val totalGeneration = historicalData.sumOf { it.totalGeneration.toDouble() }.toFloat()
    val averageEfficiency = historicalData.map { it.averageEfficiency }.average().toFloat()
    val bestPerformanceDay = historicalData.maxByOrNull { it.totalGeneration }
    val worstPerformanceDay = historicalData.minByOrNull { it.totalGeneration }
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        OverviewCard(
            title = "Total Generation",
            value = "${String.format("%.1f", totalGeneration)} kWh",
            subtitle = getTimeRangeLabel(timeRange),
            icon = Icons.Default.ElectricBolt,
            color = Color(0xFF4CAF50),
            modifier = Modifier.weight(1f)
        )
        
        OverviewCard(
            title = "Avg Efficiency",
            value = "${String.format("%.1f", averageEfficiency)}%",
            subtitle = "System performance",
            icon = Icons.Default.Speed,
            color = Color(0xFF2196F3),
            modifier = Modifier.weight(1f)
        )
    }
    
    Spacer(modifier = Modifier.height(8.dp))
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        bestPerformanceDay?.let { best ->
            OverviewCard(
                title = "Best Day",
                value = "${String.format("%.1f", best.totalGeneration)} kWh",
                subtitle = best.date, // Already a String
                icon = Icons.Default.TrendingUp,
                color = Color(0xFF4CAF50),
                modifier = Modifier.weight(1f)
            )
        }
        
        worstPerformanceDay?.let { worst ->
            OverviewCard(
                title = "Lowest Day",
                value = "${String.format("%.1f", worst.totalGeneration)} kWh",
                subtitle = worst.date, // Already a String
                icon = Icons.Default.TrendingDown,
                color = Color(0xFFFF5722),
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun OverviewCard(
    title: String,
    value: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = title,
                fontSize = 10.sp,
                color = Color.Gray
            )
            
            Text(
                text = value,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Text(
                text = subtitle,
                fontSize = 9.sp,
                color = Color.Gray
            )
        }
    }
}

@Composable
private fun TrendAnalysisSection(
    historicalData: List<HistoricalDataSet>,
    timeRange: TimeRange
) {
    val trendAnalysis = calculateTrendAnalysis(historicalData)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Trend Analysis",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                TrendIndicator(
                    label = "Generation Trend",
                    trend = trendAnalysis.generationTrend,
                    value = "${String.format("%.1f", abs(trendAnalysis.generationTrendPercentage))}%"
                )
                
                TrendIndicator(
                    label = "Efficiency Trend",
                    trend = trendAnalysis.efficiencyTrend,
                    value = "${String.format("%.1f", abs(trendAnalysis.efficiencyTrendPercentage))}%"
                )
                
                TrendIndicator(
                    label = "Performance",
                    trend = trendAnalysis.overallTrend,
                    value = getTrendDescription(trendAnalysis.overallTrend)
                )
            }
        }
    }
}

@Composable
private fun TrendIndicator(
    label: String,
    trend: TrendDirection,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = when (trend) {
                TrendDirection.UP -> Icons.Default.TrendingUp
                TrendDirection.DOWN -> Icons.Default.TrendingDown
                TrendDirection.STABLE -> Icons.Default.TrendingFlat
            },
            contentDescription = label,
            tint = when (trend) {
                TrendDirection.UP -> Color(0xFF4CAF50)
                TrendDirection.DOWN -> Color(0xFFFF5722)
                TrendDirection.STABLE -> Color(0xFFFF9800)
            },
            modifier = Modifier.size(24.dp)
        )
        
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
        
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

@Composable
private fun PerformanceComparisonSection(
    historicalData: List<HistoricalDataSet>,
    onDataSetSelected: (HistoricalDataSet) -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Performance Comparison",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyColumn(
                modifier = Modifier.height(200.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(historicalData.take(10)) { dataSet ->
                    PerformanceComparisonItem(
                        dataSet = dataSet,
                        maxGeneration = historicalData.maxOfOrNull { it.totalGeneration } ?: 1f,
                        onClick = { onDataSetSelected(dataSet) }
                    )
                }
            }
        }
    }
}

@Composable
private fun PerformanceComparisonItem(
    dataSet: HistoricalDataSet,
    maxGeneration: Float,
    onClick: () -> Unit
) {
    val progressPercentage = (dataSet.totalGeneration / maxGeneration).coerceIn(0f, 1f)
    
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                Color.White.copy(alpha = 0.05f),
                RoundedCornerShape(8.dp)
            )
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = dataSet.date.toString(),
                fontSize = 12.sp,
                color = Color.White
            )
            Text(
                text = "${String.format("%.1f", dataSet.totalGeneration)} kWh",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
        
        Box(
            modifier = Modifier
                .width(100.dp)
                .height(4.dp)
                .background(Color.Gray.copy(alpha = 0.3f), RoundedCornerShape(2.dp))
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(progressPercentage)
                    .background(
                        Color(0xFF4CAF50),
                        RoundedCornerShape(2.dp)
                    )
            )
        }
        
        Text(
            text = "${String.format("%.0f", dataSet.averageEfficiency)}%",
            fontSize = 10.sp,
            color = Color.Gray,
            modifier = Modifier.padding(start = 8.dp)
        )
    }
}

@Composable
private fun ExportControlsSection(
    onExportRequested: () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Export Historical Data",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = "Download data in various formats",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            Button(
                onClick = onExportRequested,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFE94560)
                )
            ) {
                Icon(
                    imageVector = Icons.Default.FileDownload,
                    contentDescription = "Export",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Export")
            }
        }
    }
}

@Composable
private fun ExportDataDialog(
    timeRange: TimeRange,
    onExport: (ExportFormat) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Export Historical Data")
        },
        text = {
            Column {
                Text(
                    text = "Select export format for ${getTimeRangeLabel(timeRange)} data:",
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                ExportFormat.values().forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onExport(format) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = getExportFormatIcon(format),
                            contentDescription = format.name,
                            tint = Color(0xFFE94560),
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = format.displayName,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = format.description,
                                fontSize = 12.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {},
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
private fun DataSetDetailsCard(
    dataSet: HistoricalDataSet,
    onDismiss: () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Data Details - ${dataSet.date}",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                DetailItem(
                    label = "Generation",
                    value = "${String.format("%.2f", dataSet.totalGeneration)} kWh"
                )
                DetailItem(
                    label = "Peak Power",
                    value = "${String.format("%.2f", dataSet.peakGeneration)} kW"
                )
                DetailItem(
                    label = "Efficiency",
                    value = "${String.format("%.1f", dataSet.averageEfficiency)}%"
                )
                DetailItem(
                    label = "Weather",
                    value = dataSet.weatherCondition.name.replace("_", " ")
                )
            }
        }
    }
}

@Composable
private fun DetailItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

// Using data classes from model package - removed duplicate definitions

data class TrendAnalysis(
    val generationTrend: TrendDirection,
    val generationTrendPercentage: Float,
    val efficiencyTrend: TrendDirection,
    val efficiencyTrendPercentage: Float,
    val overallTrend: TrendDirection
)

private fun calculateTrendAnalysis(data: List<HistoricalDataSet>): TrendAnalysis {
    if (data.size < 2) {
        return TrendAnalysis(
            TrendDirection.STABLE, 0f,
            TrendDirection.STABLE, 0f,
            TrendDirection.STABLE
        )
    }
    
    val sortedData = data.sortedBy { it.date }
    val firstHalf = sortedData.take(sortedData.size / 2)
    val secondHalf = sortedData.drop(sortedData.size / 2)
    
    val firstHalfAvgGeneration = firstHalf.map { it.totalGeneration }.average().toFloat()
    val secondHalfAvgGeneration = secondHalf.map { it.totalGeneration }.average().toFloat()
    val generationChange = ((secondHalfAvgGeneration - firstHalfAvgGeneration) / firstHalfAvgGeneration) * 100
    
    val firstHalfAvgEfficiency = firstHalf.map { it.averageEfficiency }.average().toFloat()
    val secondHalfAvgEfficiency = secondHalf.map { it.averageEfficiency }.average().toFloat()
    val efficiencyChange = ((secondHalfAvgEfficiency - firstHalfAvgEfficiency) / firstHalfAvgEfficiency) * 100
    
    return TrendAnalysis(
        generationTrend = when {
            generationChange > 5f -> TrendDirection.UP
            generationChange < -5f -> TrendDirection.DOWN
            else -> TrendDirection.STABLE
        },
        generationTrendPercentage = generationChange,
        efficiencyTrend = when {
            efficiencyChange > 2f -> TrendDirection.UP
            efficiencyChange < -2f -> TrendDirection.DOWN
            else -> TrendDirection.STABLE
        },
        efficiencyTrendPercentage = efficiencyChange,
        overallTrend = when {
            generationChange > 0 && efficiencyChange > 0 -> TrendDirection.UP
            generationChange < 0 && efficiencyChange < 0 -> TrendDirection.DOWN
            else -> TrendDirection.STABLE
        }
    )
}

private fun getTimeRangeLabel(timeRange: TimeRange): String {
    return when (timeRange) {
        TimeRange.WEEK -> "Last 7 days"
        TimeRange.MONTH -> "Last 30 days"
        TimeRange.YEAR -> "Last 12 months"
        TimeRange.ALL_TIME -> "All time"
        else -> timeRange.name
    }
}

private fun getTrendDescription(trend: TrendDirection): String {
    return when (trend) {
        TrendDirection.UP -> "Improving"
        TrendDirection.DOWN -> "Declining"
        TrendDirection.STABLE -> "Stable"
    }
}

private fun getExportFormatIcon(format: ExportFormat): androidx.compose.ui.graphics.vector.ImageVector {
    return when (format) {
        ExportFormat.CSV -> Icons.Default.TableChart
        ExportFormat.JSON -> Icons.Default.Code
        ExportFormat.PDF -> Icons.Default.Description
        ExportFormat.EXCEL -> Icons.Default.GridOn
    }
}

@Composable
fun AdvancedExportDialog(
    timeRange: TimeRange,
    exportStatus: ExportStatus?,
    onExport: (ExportFormat) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Export Data",
                color = Color.White,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                Text(
                    text = "Select export format for ${timeRange.name.lowercase()} data:",
                    color = Color.White.copy(alpha = 0.8f),
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                ExportFormat.values().forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onExport(format) }
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = getExportFormatIcon(format),
                            contentDescription = null,
                            tint = Color(0xFF00E5FF),
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = format.displayName,
                            color = Color.White,
                            fontSize = 16.sp
                        )
                    }
                }

                exportStatus?.let { status ->
                    if (status.isExporting) {
                        Spacer(modifier = Modifier.height(16.dp))
                        LinearProgressIndicator(
                            progress = { status.progress / 100f },
                            modifier = Modifier.fillMaxWidth(),
                            color = Color(0xFF00E5FF)
                        )
                        Text(
                            text = "Exporting... ${status.progress.toInt()}%",
                            color = Color.White.copy(alpha = 0.7f),
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }

                    status.error?.let { error ->
                        Text(
                            text = "Error: $error",
                            color = Color.Red,
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close", color = Color(0xFF00E5FF))
            }
        },
        containerColor = Color.Black.copy(alpha = 0.9f)
    )
}

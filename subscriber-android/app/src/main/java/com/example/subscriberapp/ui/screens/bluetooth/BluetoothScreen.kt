package com.example.subscriberapp.ui.screens.bluetooth

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.services.CheckInResponse
import com.example.subscriberapp.services.CheckInResult
import com.example.subscriberapp.services.CyclingResultsManager
import com.example.subscriberapp.ui.components.GradientButton
import com.example.subscriberapp.ui.components.NeonCard
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val cyclingManager = remember { CyclingResultsManager(context) }
    
    var bluetoothState by remember { mutableStateOf(BluetoothState.READY) }
    var bluetoothResult by remember { mutableStateOf<CheckInResponse?>(null) }
    
    LaunchedEffect(bluetoothState) {
        when (bluetoothState) {
            BluetoothState.SCANNING -> {
                delay(2500) // Simulate 2.5 seconds of Bluetooth scanning
                val result = cyclingManager.getBluetoothResult()
                bluetoothResult = result
                bluetoothState = BluetoothState.RESULT
            }
            BluetoothState.RESULT -> {
                delay(3000) // Show result for 3 seconds
                bluetoothState = BluetoothState.READY
                bluetoothResult = null
            }
            else -> {}
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Bluetooth Check-in",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Main Content
            when (bluetoothState) {
                BluetoothState.READY -> {
                    BluetoothReadyContent(
                        onStartBluetooth = { bluetoothState = BluetoothState.SCANNING }
                    )
                }
                BluetoothState.SCANNING -> {
                    BluetoothScanningContent()
                }
                BluetoothState.RESULT -> {
                    bluetoothResult?.let { result ->
                        BluetoothResultContent(result = result)
                    }
                }
            }
        }
    }
}

enum class BluetoothState {
    READY, SCANNING, RESULT
}

@Composable
fun BluetoothReadyContent(onStartBluetooth: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF2196F3)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Bluetooth,
                    contentDescription = "Bluetooth",
                    tint = Color(0xFF2196F3),
                    modifier = Modifier.size(80.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Bluetooth Check-in",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "Scan for nearby Bluetooth beacons to check-in automatically",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Mock beacon info
                NeonCard(
                    modifier = Modifier.fillMaxWidth(),
                    glowColor = Color(0xFF2196F3).copy(alpha = 0.5f)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Looking for:",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        Text(
                            text = "AttendanceBeacon_001",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        Text(
                            text = "Range: 10m • Type: BLE Beacon",
                            fontSize = 12.sp,
                            color = Color(0xFF2196F3),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                GradientButton(
                    text = "Start Bluetooth Scan",
                    onClick = onStartBluetooth,
                    modifier = Modifier.fillMaxWidth(),
                    colors = listOf(Color(0xFF2196F3), Color(0xFF1976D2))
                )
            }
        }
    }
}

@Composable
fun BluetoothScanningContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF2196F3)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Bluetooth scanning animation
                Box(
                    modifier = Modifier
                        .size(200.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFF2196F3).copy(alpha = 0.3f),
                                    Color.Transparent
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Bluetooth,
                            contentDescription = "Bluetooth Scanning",
                            tint = Color(0xFF2196F3),
                            modifier = Modifier.size(80.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Scanning waves animation
                        repeat(3) { index ->
                            Box(
                                modifier = Modifier
                                    .size((60 + index * 20).dp)
                                    .background(
                                        Color(0xFF2196F3).copy(alpha = 0.2f - index * 0.05f),
                                        RoundedCornerShape(50)
                                    )
                            )
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Scanning for Beacons...",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = "Searching for nearby devices",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Scanning progress
                LinearProgressIndicator(
                    color = Color(0xFF2196F3),
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Found devices list
                Column {
                    ScanningDevice("AttendanceBeacon_001", "10m", true)
                    ScanningDevice("AttendanceBeacon_002", "25m", false)
                    ScanningDevice("AttendanceBeacon_003", "Out of range", false)
                }
            }
        }
    }
}

@Composable
fun ScanningDevice(name: String, distance: String, found: Boolean) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (found) {
            Text(
                text = "📶",
                fontSize = 16.sp
            )
        } else {
            CircularProgressIndicator(
                color = Color(0xFF2196F3),
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = name,
                color = if (found) Color.White else Color.Gray,
                fontSize = 14.sp,
                fontWeight = if (found) FontWeight.Bold else FontWeight.Normal
            )
            Text(
                text = distance,
                color = Color.Gray,
                fontSize = 12.sp
            )
        }
    }
}

@Composable
fun BluetoothResultContent(result: CheckInResponse) {
    val resultColor = when (result.result) {
        CheckInResult.SUCCESS -> Color(0xFF4CAF50)
        CheckInResult.EXPIRED -> Color(0xFFFF9800)
        CheckInResult.INVALID -> Color(0xFFF44336)
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = resultColor
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = result.icon,
                    fontSize = 64.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = result.title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = result.message,
                    fontSize = 16.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

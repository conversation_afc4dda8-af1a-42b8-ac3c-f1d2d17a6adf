package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime import - using String for dates

@Composable
fun TaskManagement(
    task: MaintenanceTask,
    onChecklistItemToggle: (String, Boolean) -> Unit,
    onTaskStatusChange: (TaskStatus) -> Unit,
    onAddNote: (String, String) -> Unit,
    onTakePhoto: (String) -> Unit,
    onCompleteTask: () -> Unit,
    modifier: Modifier = Modifier
) {
    var showCompleteDialog by remember { mutableStateOf(false) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Task Header
        TaskHeader(
            task = task,
            onStatusChange = onTaskStatusChange,
            onCompleteTask = { showCompleteDialog = true }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Task Progress
        TaskProgress(task = task)
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Interactive Checklist
        InteractiveChecklist(
            checklist = task.checklist,
            onItemToggle = onChecklistItemToggle,
            onAddNote = onAddNote,
            onTakePhoto = onTakePhoto
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Task Details
        TaskDetails(task = task)
        
        // Complete Task Dialog
        if (showCompleteDialog) {
            CompleteTaskDialog(
                task = task,
                onConfirm = {
                    onCompleteTask()
                    showCompleteDialog = false
                },
                onDismiss = { showCompleteDialog = false }
            )
        }
    }
}

@Composable
private fun TaskHeader(
    task: MaintenanceTask,
    onStatusChange: (TaskStatus) -> Unit,
    onCompleteTask: () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = task.title,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    
                    Text(
                        text = task.description,
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                // Priority Badge
                PriorityBadge(priority = task.priority)
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Task Info Row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                TaskInfoItem(
                    icon = Icons.Default.CalendarToday,
                    label = "Scheduled",
                    value = task.scheduledDate.toString()
                )
                
                TaskInfoItem(
                    icon = Icons.Default.Schedule,
                    label = "Duration",
                    value = task.estimatedDuration // Already a String
                )
                
                TaskInfoItem(
                    icon = Icons.Default.AttachMoney,
                    label = "Cost",
                    value = "$${String.format("%.0f", task.cost)}"
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Action Buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                if (task.status == TaskStatus.SCHEDULED) {
                    Button(
                        onClick = { onStatusChange(TaskStatus.IN_PROGRESS) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF2196F3)
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Start Task",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Start Task")
                    }
                }
                
                if (task.status == TaskStatus.IN_PROGRESS) {
                    Button(
                        onClick = onCompleteTask,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4CAF50)
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = "Complete Task",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Complete")
                    }
                    
                    OutlinedButton(
                        onClick = { onStatusChange(TaskStatus.SCHEDULED) },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Pause,
                            contentDescription = "Pause Task",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Pause")
                    }
                }
            }
        }
    }
}

@Composable
private fun PriorityBadge(priority: MaintenancePriority) {
    val (color, text) = when (priority) {
        MaintenancePriority.LOW -> Color(0xFF4CAF50) to "LOW"
        MaintenancePriority.MEDIUM -> Color(0xFFFF9800) to "MED"
        MaintenancePriority.HIGH -> Color(0xFFFF5722) to "HIGH"
        MaintenancePriority.CRITICAL -> Color(0xFFF44336) to "CRIT"
    }
    
    Box(
        modifier = Modifier
            .background(
                color = color.copy(alpha = 0.2f),
                shape = RoundedCornerShape(12.dp)
            )
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = text,
            fontSize = 10.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
private fun TaskInfoItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = Color(0xFFE94560),
            modifier = Modifier.size(20.dp)
        )
        
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray,
            modifier = Modifier.padding(top = 4.dp)
        )
        
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White
        )
    }
}

@Composable
private fun TaskProgress(task: MaintenanceTask) {
    val completedItems = task.checklist.count { it.isCompleted }
    val totalItems = task.checklist.size
    val progress = if (totalItems > 0) completedItems.toFloat() / totalItems else 0f
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Task Progress",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = "$completedItems / $totalItems completed",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = { progress },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = when {
                    progress >= 1f -> Color(0xFF4CAF50)
                    progress >= 0.5f -> Color(0xFFFF9800)
                    else -> Color(0xFFE94560)
                },
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "${(progress * 100).toInt()}% Complete",
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
        }
    }
}

@Composable
private fun InteractiveChecklist(
    checklist: List<ChecklistItem>,
    onItemToggle: (String, Boolean) -> Unit,
    onAddNote: (String, String) -> Unit,
    onTakePhoto: (String) -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Maintenance Checklist",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyColumn(
                modifier = Modifier.height(300.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(checklist) { item ->
                    ChecklistItemCard(
                        item = item,
                        onToggle = { onItemToggle(item.id, !item.isCompleted) },
                        onAddNote = { note -> onAddNote(item.id, note) },
                        onTakePhoto = { onTakePhoto(item.id) }
                    )
                }
            }
        }
    }
}

@Composable
private fun ChecklistItemCard(
    item: ChecklistItem,
    onToggle: () -> Unit,
    onAddNote: (String) -> Unit,
    onTakePhoto: () -> Unit
) {
    var showNoteDialog by remember { mutableStateOf(false) }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = if (item.isCompleted) 
                Color(0xFF4CAF50).copy(alpha = 0.1f) 
            else 
                Color.White.copy(alpha = 0.05f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = item.isCompleted,
                    onCheckedChange = { onToggle() },
                    colors = CheckboxDefaults.colors(
                        checkedColor = Color(0xFF4CAF50),
                        uncheckedColor = Color.Gray
                    )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = item.description,
                    fontSize = 14.sp,
                    color = if (item.isCompleted) Color.Gray else Color.White,
                    modifier = Modifier.weight(1f)
                )
            }
            
            if (item.notes != null) {
                Text(
                    text = "Note: ${item.notes}",
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(start = 40.dp, top = 4.dp)
                )
            }
            
            if (item.photoUrl != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(start = 40.dp, top = 4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Photo,
                        contentDescription = "Photo attached",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Photo attached",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50)
                    )
                }
            }
            
            // Action buttons
            Row(
                modifier = Modifier.padding(start = 40.dp, top = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = { showNoteDialog = true }
                ) {
                    Icon(
                        imageVector = Icons.Default.Note,
                        contentDescription = "Add Note",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Note", fontSize = 12.sp)
                }
                
                if (item.photoRequired) {
                    TextButton(
                        onClick = onTakePhoto
                    ) {
                        Icon(
                            imageVector = Icons.Default.CameraAlt,
                            contentDescription = "Take Photo",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Photo", fontSize = 12.sp)
                    }
                }
            }
        }
    }
    
    // Note Dialog
    if (showNoteDialog) {
        AddNoteDialog(
            currentNote = item.notes ?: "",
            onSave = { note ->
                onAddNote(note)
                showNoteDialog = false
            },
            onDismiss = { showNoteDialog = false }
        )
    }
}

@Composable
private fun TaskDetails(task: MaintenanceTask) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Task Details",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            DetailRow(
                label = "Assigned Technician",
                value = task.assignedTechnician ?: "Not assigned"
            )
            
            DetailRow(
                label = "Components",
                value = task.components.joinIfEmpty("None specified")
            )
            
            DetailRow(
                label = "Status",
                value = task.status.name.replace("_", " ").lowercase().replaceFirstChar { it.uppercase() }
            )
        }
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        
        Text(
            text = value,
            fontSize = 12.sp,
            color = Color.White,
            modifier = Modifier.weight(1f),
            textAlign = androidx.compose.ui.text.style.TextAlign.End
        )
    }
}

@Composable
private fun CompleteTaskDialog(
    task: MaintenanceTask,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Complete Task")
        },
        text = {
            Column {
                Text("Are you sure you want to mark this task as completed?")
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Task: ${task.title}",
                    fontWeight = FontWeight.Bold
                )
                
                val completedItems = task.checklist.count { it.isCompleted }
                val totalItems = task.checklist.size
                
                if (completedItems < totalItems) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Warning: Only $completedItems of $totalItems checklist items are completed.",
                        color = Color(0xFFFF9800)
                    )
                }
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF4CAF50)
                )
            ) {
                Text("Complete")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
private fun AddNoteDialog(
    currentNote: String,
    onSave: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var noteText by remember { mutableStateOf(currentNote) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("Add Note")
        },
        text = {
            OutlinedTextField(
                value = noteText,
                onValueChange = { noteText = it },
                label = { Text("Note") },
                placeholder = { Text("Enter your note here...") },
                maxLines = 4,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            Button(
                onClick = { onSave(noteText) },
                enabled = noteText.isNotBlank()
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

// Helper extension function
private fun List<String>.joinIfEmpty(default: String): String {
    return if (isEmpty()) default else joinToString(", ")
}

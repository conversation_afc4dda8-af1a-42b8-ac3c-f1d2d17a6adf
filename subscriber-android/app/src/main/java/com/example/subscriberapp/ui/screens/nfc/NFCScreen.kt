package com.example.subscriberapp.ui.screens.nfc

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Nfc
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.services.CheckInResponse
import com.example.subscriberapp.services.CheckInResult
import com.example.subscriberapp.services.CyclingResultsManager
import com.example.subscriberapp.ui.components.GradientButton
import com.example.subscriberapp.ui.components.NeonCard
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NFCScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val cyclingManager = remember { CyclingResultsManager(context) }
    
    var nfcState by remember { mutableStateOf(NFCState.READY) }
    var nfcResult by remember { mutableStateOf<CheckInResponse?>(null) }
    
    LaunchedEffect(nfcState) {
        when (nfcState) {
            NFCState.SCANNING -> {
                delay(2000) // Simulate 2 seconds of NFC detection
                val result = cyclingManager.getNFCResult()
                nfcResult = result
                nfcState = NFCState.RESULT
            }
            NFCState.RESULT -> {
                delay(3000) // Show result for 3 seconds
                nfcState = NFCState.READY
                nfcResult = null
            }
            else -> {}
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "NFC Check-in",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Main Content
            when (nfcState) {
                NFCState.READY -> {
                    NFCReadyContent(
                        onStartNFC = { nfcState = NFCState.SCANNING }
                    )
                }
                NFCState.SCANNING -> {
                    NFCScanningContent()
                }
                NFCState.RESULT -> {
                    nfcResult?.let { result ->
                        NFCResultContent(result = result)
                    }
                }
            }
        }
    }
}

enum class NFCState {
    READY, SCANNING, RESULT
}

@Composable
fun NFCReadyContent(onStartNFC: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF4ECDC4)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Nfc,
                    contentDescription = "NFC",
                    tint = Color(0xFF4ECDC4),
                    modifier = Modifier.size(80.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "NFC Ready",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "Tap your NFC card on the back of your device",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                GradientButton(
                    text = "Enable NFC",
                    onClick = onStartNFC,
                    modifier = Modifier.fillMaxWidth(),
                    colors = listOf(Color(0xFF4ECDC4), Color(0xFF44A08D))
                )
            }
        }
    }
}

@Composable
fun NFCScanningContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF4ECDC4)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // NFC scanning animation
                Box(
                    modifier = Modifier
                        .size(200.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFF4ECDC4).copy(alpha = 0.3f),
                                    Color.Transparent
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Nfc,
                        contentDescription = "NFC Scanning",
                        tint = Color(0xFF4ECDC4),
                        modifier = Modifier.size(80.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Detecting NFC...",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = "Hold your card steady",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                LinearProgressIndicator(
                    color = Color(0xFF4ECDC4),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun NFCResultContent(result: CheckInResponse) {
    val resultColor = when (result.result) {
        CheckInResult.SUCCESS -> Color(0xFF4CAF50)
        CheckInResult.EXPIRED -> Color(0xFFFF9800)
        CheckInResult.INVALID -> Color(0xFFF44336)
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = resultColor
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = result.icon,
                    fontSize = 64.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = result.title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = result.message,
                    fontSize = 16.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

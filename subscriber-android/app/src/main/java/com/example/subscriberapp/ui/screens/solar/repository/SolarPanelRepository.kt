package com.example.subscriberapp.ui.screens.solar.repository

import com.example.subscriberapp.ui.screens.solar.model.*
import com.example.subscriberapp.ui.screens.solar.viewmodel.RealTimeUpdates
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.*
import java.text.SimpleDateFormat
import java.util.concurrent.TimeUnit
import kotlin.random.Random

class SolarPanelRepository {

    private val mockDataService = MockSolarDataService()

    suspend fun getSystemStatus(): SystemStatus {
        delay(500) // Simulate network delay
        return mockDataService.generateSystemStatus()
    }

    suspend fun getEnergyFlowData(): EnergyFlowData {
        delay(300)
        return mockDataService.generateEnergyFlowData()
    }

    suspend fun getBatteryStatus(): BatteryStatus {
        delay(400)
        return mockDataService.generateBatteryStatus()
    }

    suspend fun getPowerGenerationData(timeRange: TimeRange = TimeRange.DAY): PowerGenerationData {
        delay(600)
        return mockDataService.generatePowerGenerationData(timeRange)
    }

    suspend fun getSystemHealthStatus(): SystemHealthStatus {
        delay(500)
        return mockDataService.generateSystemHealthStatus()
    }

    suspend fun getMaintenanceData(): MaintenanceData {
        delay(400)
        return mockDataService.generateMaintenanceData()
    }

    fun getRealTimeUpdates(): Flow<RealTimeUpdates> = flow {
        while (true) {
            delay(5000) // Update every 5 seconds
            emit(
                RealTimeUpdates(
                    systemStatus = mockDataService.generateSystemStatus(),
                    energyFlow = mockDataService.generateEnergyFlowData(),
                    batteryStatus = mockDataService.generateBatteryStatus()
                )
            )
        }
    }

    suspend fun acknowledgeAlert(alertId: String) {
        delay(200)
        mockDataService.acknowledgeAlert(alertId)
    }

    suspend fun completeMaintenanceTask(taskId: String) {
        delay(300)
        mockDataService.completeMaintenanceTask(taskId)
    }

    suspend fun scheduleMaintenanceTask(task: MaintenanceTask) {
        delay(250)
        mockDataService.scheduleMaintenanceTask(task)
    }

    suspend fun getHistoricalData(timeRange: TimeRange): List<HistoricalDataSet> {
        delay(400)
        return mockDataService.generateHistoricalData(timeRange)
    }

    suspend fun getPerformanceMetrics(metricType: PerformanceMetricType): List<PerformanceMetric> {
        delay(300)
        return mockDataService.generatePerformanceMetrics(metricType)
    }

    suspend fun exportData(format: ExportFormat, timeRange: TimeRange): String {
        delay(500)
        return mockDataService.exportData(format, timeRange)
    }

    suspend fun getMaintenanceNotifications(): List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceNotification> {
        delay(300)
        return mockDataService.generateMaintenanceNotifications()
    }

    suspend fun getMaintenanceHistory(timeRange: TimeRange): List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceRecord> {
        delay(400)
        return mockDataService.generateMaintenanceHistory(timeRange)
    }
}

/**
 * Mock Data Service for generating realistic solar panel data
 */
private class MockSolarDataService {
    
    private val random = Random.Default
    private val currentTime = System.currentTimeMillis()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val dateTimeFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    private val acknowledgedAlerts = mutableSetOf<String>()
    private val completedTasks = mutableSetOf<String>()
    private val scheduledTasks = mutableListOf<MaintenanceTask>()

    fun generateSystemStatus(): SystemStatus {
        val efficiency = 85f + random.nextFloat() * 10f // 85-95%
        val currentGeneration = if (isDay()) {
            3.5f + random.nextFloat() * 2f // 3.5-5.5 kW during day
        } else {
            0f // No generation at night
        }
        val batteryLevel = 60f + random.nextFloat() * 35f // 60-95%

        return SystemStatus(
            overallStatus = when {
                efficiency > 90f -> SystemStatusType.NORMAL
                efficiency > 80f -> SystemStatusType.WARNING
                else -> SystemStatusType.CRITICAL
            },
            efficiency = efficiency,
            generationStatus = if (currentGeneration > 3f) SystemStatusType.NORMAL else SystemStatusType.WARNING,
            currentGeneration = currentGeneration,
            batteryStatus = when {
                batteryLevel > 80f -> SystemStatusType.NORMAL
                batteryLevel > 30f -> SystemStatusType.WARNING
                else -> SystemStatusType.CRITICAL
            },
            batteryLevel = batteryLevel,
            lastUpdated = getDateTimeString(0)
        )
    }

    fun generateEnergyFlowData(): EnergyFlowData {
        val generation = if (isDay()) 4.2f + random.nextFloat() * 1.5f else 0f
        val voltage = 230f + random.nextFloat() * 20f
        val current = if (generation > 0) generation / voltage * 1000f else 0f
        
        val routingMode = when {
            generation > 4f -> EnergyRoutingMode.BATTERY_CHARGING
            generation > 2f -> EnergyRoutingMode.HOUSEHOLD_DIRECT
            generation == 0f -> EnergyRoutingMode.BATTERY_DISCHARGING
            else -> EnergyRoutingMode.HYBRID
        }

        return EnergyFlowData(
            currentGeneration = generation,
            voltage = voltage,
            current = current,
            routingMode = routingMode,
            flowRate = generation,
            powerToHousehold = minOf(generation, 2.5f),
            powerToBattery = maxOf(0f, generation - 2.5f),
            powerFromGrid = if (generation < 2.5f) 2.5f - generation else 0f
        )
    }

    fun generateBatteryStatus(): BatteryStatus {
        val chargePercentage = 65f + random.nextFloat() * 30f
        val isCharging = isDay() && random.nextBoolean()
        val chargeRate = if (isCharging) 1.5f + random.nextFloat() * 1f else 0f
        val dischargeRate = if (!isCharging && !isDay()) 0.8f + random.nextFloat() * 0.5f else 0f

        return BatteryStatus(
            chargePercentage = chargePercentage,
            healthPercentage = 92f + random.nextFloat() * 6f,
            isCharging = isCharging,
            chargeRate = chargeRate,
            dischargeRate = dischargeRate,
            temperature = 25f + random.nextFloat() * 10f,
            remainingCapacity = chargePercentage * 0.1f, // 10kWh total capacity
            totalCapacity = 10f,
            estimatedTimeToFull = if (isCharging) getDurationString(4 + random.nextInt(4)) else null,
            estimatedTimeToEmpty = if (dischargeRate > 0) getDurationString(8 + random.nextInt(8)) else null,
            cycleCount = 1200 + random.nextInt(300),
            lastMaintenanceDate = getDateString(-90) // 3 months ago
        )
    }

    fun generatePowerGenerationData(timeRange: TimeRange): PowerGenerationData {
        val todayData = generateHourlyData()
        val monthlyData = generateDailyData()
        
        return PowerGenerationData(
            todayGeneration = todayData,
            monthlyGeneration = monthlyData,
            totalGeneratedToday = todayData.sumOf { it.powerGenerated.toDouble() }.toFloat(),
            totalGeneratedThisMonth = monthlyData.sumOf { it.totalGenerated.toDouble() }.toFloat(),
            totalGeneratedAllTime = 15000f + random.nextFloat() * 5000f,
            peakGenerationToday = todayData.maxOfOrNull { it.powerGenerated } ?: 0f,
            averageEfficiency = 87f + random.nextFloat() * 8f,
            costSavingsToday = 12.5f + random.nextFloat() * 5f,
            costSavingsThisMonth = 350f + random.nextFloat() * 100f,
            co2SavedToday = 8.2f + random.nextFloat() * 3f,
            weatherImpact = WeatherImpact(
                currentCondition = WeatherCondition.values().random(),
                temperature = 22f + random.nextFloat() * 15f,
                cloudCover = random.nextFloat() * 100f,
                expectedImpact = -20f + random.nextFloat() * 40f
            )
        )
    }

    fun generateSystemHealthStatus(): SystemHealthStatus {
        val panels = generatePanelStatuses()
        val alerts = generateAlerts()
        
        return SystemHealthStatus(
            overallHealth = 88f + random.nextFloat() * 10f,
            solarPanels = panels,
            inverterStatus = InverterStatus(
                health = 94f + random.nextFloat() * 5f,
                efficiency = 95f + random.nextFloat() * 3f,
                temperature = 45f + random.nextFloat() * 15f,
                status = ComponentStatus.NORMAL,
                inputVoltage = 400f + random.nextFloat() * 50f,
                outputVoltage = 230f + random.nextFloat() * 10f,
                frequency = 50f + random.nextFloat() * 0.5f,
                lastMaintenance = getDateString(-180) // 6 months ago
            ),
            wiringStatus = WiringStatus(
                overallHealth = 96f + random.nextFloat() * 3f,
                dcWiring = ComponentStatus.NORMAL,
                acWiring = ComponentStatus.NORMAL,
                groundingStatus = ComponentStatus.NORMAL,
                connectionIntegrity = 98f + random.nextFloat() * 2f,
                lastInspection = getDateString(-365) // 12 months ago
            ),
            meterStatus = MeterStatus(
                isConnected = true,
                communicationStatus = ComponentStatus.NORMAL,
                dataAccuracy = 99f + random.nextFloat() * 1f,
                lastReading = getDateTimeString(-1), // 1 hour ago
                totalEnergyExported = 12500f + random.nextFloat() * 2000f,
                totalEnergyImported = 3200f + random.nextFloat() * 500f
            ),
            alerts = alerts.filterNot { acknowledgedAlerts.contains(it.id) },
            lastHealthCheck = getDateTimeString(-24) // 24 hours ago
        )
    }

    fun generateMaintenanceData(): MaintenanceData {
        val upcomingTasks = generateMaintenanceTasks()
        val completedTasksList = generateCompletedTasks()
        
        return MaintenanceData(
            upcomingTasks = upcomingTasks.filterNot { completedTasks.contains(it.id) } + scheduledTasks,
            completedTasks = completedTasksList,
            overdueTasks = upcomingTasks.filter {
                // Simple string comparison - tasks scheduled before today are overdue
                it.scheduledDate < getDateString(0) && !completedTasks.contains(it.id)
            },
            nextScheduledMaintenance = getDateString(90), // 3 months from now
            totalMaintenanceCost = 2500f + random.nextFloat() * 1000f,
            maintenanceHistory = generateMaintenanceHistory(TimeRange.MONTH)
        )
    }

    private fun generateHourlyData(): List<HourlyGenerationData> {
        return (0..23).map { hour ->
            val generation = when (hour) {
                in 6..8 -> random.nextFloat() * 2f
                in 9..11 -> 2f + random.nextFloat() * 3f
                in 12..14 -> 4f + random.nextFloat() * 2f
                in 15..17 -> 2f + random.nextFloat() * 3f
                in 18..19 -> random.nextFloat() * 1.5f
                else -> 0f
            }
            
            HourlyGenerationData(
                hour = hour,
                powerGenerated = generation,
                efficiency = if (generation > 0) 80f + random.nextFloat() * 15f else 0f,
                weatherCondition = WeatherCondition.values().random(),
                temperature = 20f + random.nextFloat() * 20f
            )
        }
    }

    private fun generateDailyData(): List<DailyGenerationData> {
        return (1..30).map { day ->
            DailyGenerationData(
                date = getDateString(-(30 - day)), // Days ago
                totalGenerated = 15f + random.nextFloat() * 20f,
                peakGeneration = 4f + random.nextFloat() * 2f,
                averageEfficiency = 80f + random.nextFloat() * 15f,
                weatherCondition = WeatherCondition.values().random()
            )
        }
    }

    private fun generatePanelStatuses(): List<PanelStatus> {
        return (1..20).map { index ->
            PanelStatus(
                id = "PANEL_${index.toString().padStart(3, '0')}",
                position = PanelPosition(
                    row = (index - 1) / 5 + 1,
                    column = (index - 1) % 5 + 1,
                    orientation = "South"
                ),
                health = 85f + random.nextFloat() * 12f,
                currentOutput = if (isDay()) 250f + random.nextFloat() * 50f else 0f,
                maxOutput = 300f,
                temperature = 30f + random.nextFloat() * 20f,
                status = ComponentStatus.values().random(),
                lastCleaned = getDateString(-random.nextInt(90)) // Random days ago
            )
        }
    }

    private fun generateAlerts(): List<SystemAlert> {
        return listOf(
            SystemAlert(
                id = "ALERT_001",
                title = "Panel Cleaning Required",
                description = "Solar panels efficiency has decreased by 5% due to dust accumulation",
                severity = AlertSeverity.WARNING,
                component = "Solar Panels",
                timestamp = getDateTimeString(-2), // 2 hours ago
                actionRequired = "Schedule panel cleaning"
            ),
            SystemAlert(
                id = "ALERT_002",
                title = "Inverter Temperature High",
                description = "Inverter temperature is above normal operating range",
                severity = AlertSeverity.CRITICAL,
                component = "Inverter",
                timestamp = getDateTimeString(0), // 30 minutes ago (simplified to current time)
                actionRequired = "Check inverter ventilation"
            )
        )
    }

    private fun generateMaintenanceTasks(): List<MaintenanceTask> {
        return listOf(
            MaintenanceTask(
                id = "TASK_001",
                title = "Quarterly Panel Cleaning",
                description = "Clean all solar panels and check for physical damage",
                scheduledDate = getDateString(7), // 7 days from now
                priority = MaintenancePriority.MEDIUM,
                estimatedDuration = getDurationString(4), // 4 hours
                status = TaskStatus.SCHEDULED,
                cost = 200f,
                components = listOf("Solar Panels")
            ),
            MaintenanceTask(
                id = "TASK_002",
                title = "Inverter Inspection",
                description = "Annual inverter performance check and firmware update",
                scheduledDate = getDateString(30), // 30 days from now
                priority = MaintenancePriority.HIGH,
                estimatedDuration = getDurationString(2), // 2 hours
                status = TaskStatus.SCHEDULED,
                cost = 150f,
                components = listOf("Inverter")
            )
        )
    }

    private fun generateCompletedTasks(): List<MaintenanceTask> {
        return listOf(
            MaintenanceTask(
                id = "TASK_COMPLETED_001",
                title = "Battery Health Check",
                description = "Monthly battery performance and health assessment",
                scheduledDate = getDateString(-15), // 15 days ago
                priority = MaintenancePriority.MEDIUM,
                estimatedDuration = getDurationString(1), // 1 hour
                status = TaskStatus.COMPLETED,
                cost = 100f,
                components = listOf("Battery")
            )
        )
    }

    // Old generateMaintenanceHistory method removed - using new one below

    private fun isDay(): Boolean {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        return hour in 6..18
    }

    private fun getDateString(offsetDays: Int = 0): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = currentTime
        calendar.add(Calendar.DAY_OF_YEAR, offsetDays)
        return dateFormat.format(calendar.time)
    }

    private fun getDateTimeString(offsetHours: Int = 0): String {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = currentTime
        calendar.add(Calendar.HOUR_OF_DAY, offsetHours)
        return dateTimeFormat.format(calendar.time)
    }

    private fun getDurationString(hours: Int): String {
        return if (hours < 24) {
            "$hours hours"
        } else {
            val days = hours / 24
            val remainingHours = hours % 24
            if (remainingHours == 0) {
                "$days days"
            } else {
                "$days days $remainingHours hours"
            }
        }
    }

    fun acknowledgeAlert(alertId: String) {
        acknowledgedAlerts.add(alertId)
    }

    fun completeMaintenanceTask(taskId: String) {
        completedTasks.add(taskId)
    }

    fun scheduleMaintenanceTask(task: MaintenanceTask) {
        scheduledTasks.add(task)
    }

    fun generateHistoricalData(timeRange: TimeRange): List<HistoricalDataSet> {
        val days = when (timeRange) {
            TimeRange.WEEK -> 7
            TimeRange.MONTH -> 30
            TimeRange.YEAR -> 365
            else -> 90
        }

        return (1..days).map { dayOffset ->
            HistoricalDataSet(
                date = getDateString(-dayOffset), // dayOffset days ago
                totalGeneration = 15f + random.nextFloat() * 25f, // 15-40 kWh per day
                peakGeneration = 4f + random.nextFloat() * 3f, // 4-7 kW peak
                averageEfficiency = 80f + random.nextFloat() * 15f, // 80-95% efficiency
                weatherCondition = WeatherCondition.values().random()
            )
        }.sortedBy { it.date }
    }

    fun generatePerformanceMetrics(metricType: PerformanceMetricType): List<PerformanceMetric> {
        return (1..30).map { dayOffset ->
            val baseValue = when (metricType) {
                PerformanceMetricType.GENERATION -> 20f + random.nextFloat() * 15f
                PerformanceMetricType.EFFICIENCY -> 85f + random.nextFloat() * 10f
                PerformanceMetricType.BATTERY_HEALTH -> 90f + random.nextFloat() * 8f
                PerformanceMetricType.INVERTER_PERFORMANCE -> 92f + random.nextFloat() * 6f
                PerformanceMetricType.WEATHER_IMPACT -> 75f + random.nextFloat() * 20f
                PerformanceMetricType.COST_SAVINGS -> 15f + random.nextFloat() * 10f
            }

            val unit = when (metricType) {
                PerformanceMetricType.GENERATION -> "kWh"
                PerformanceMetricType.EFFICIENCY -> "%"
                PerformanceMetricType.BATTERY_HEALTH -> "%"
                PerformanceMetricType.INVERTER_PERFORMANCE -> "%"
                PerformanceMetricType.WEATHER_IMPACT -> "%"
                PerformanceMetricType.COST_SAVINGS -> "USD"
            }

            PerformanceMetric(
                type = metricType,
                value = baseValue,
                unit = unit,
                timestamp = getDateTimeString(-dayOffset * 24), // dayOffset days ago
                confidenceLevel = 90f + random.nextFloat() * 8f
            )
        }.sortedBy { it.timestamp }
    }

    fun exportData(format: ExportFormat, timeRange: TimeRange): String {
        // Simulate file path generation
        val timestamp = System.currentTimeMillis()
        val fileName = "solar_data_${timeRange.name.lowercase()}_$timestamp.${format.extension}"
        val filePath = "/storage/emulated/0/Download/$fileName"

        return when (format) {
            ExportFormat.CSV -> filePath
            ExportFormat.JSON -> filePath
            ExportFormat.PDF -> filePath
            ExportFormat.EXCEL -> filePath
        }
    }

    fun generateMaintenanceNotifications(): List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceNotification> {
        return listOf(
            com.example.subscriberapp.ui.screens.solar.components.MaintenanceNotification(
                id = "notif_1",
                type = com.example.subscriberapp.ui.screens.solar.components.NotificationType.MAINTENANCE_DUE,
                priority = com.example.subscriberapp.ui.screens.solar.components.NotificationPriority.HIGH,
                title = "Panel Cleaning Due",
                message = "Solar panels require cleaning for optimal performance",
                scheduledTime = getDateTimeString(48), // 2 days from now
                taskId = "task_1",
                isRead = false
            ),
            com.example.subscriberapp.ui.screens.solar.components.MaintenanceNotification(
                id = "notif_2",
                type = com.example.subscriberapp.ui.screens.solar.components.NotificationType.SYSTEM_ALERT,
                priority = com.example.subscriberapp.ui.screens.solar.components.NotificationPriority.MEDIUM,
                title = "Inverter Performance",
                message = "Inverter efficiency has decreased by 5%",
                scheduledTime = getDateTimeString(-4), // 4 hours ago
                isRead = true
            )
        )
    }

    fun generateMaintenanceHistory(timeRange: TimeRange): List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceRecord> {
        val days = when (timeRange) {
            TimeRange.MONTH -> 30
            TimeRange.QUARTER -> 90
            TimeRange.YEAR -> 365
            else -> 180
        }

        return (1..10).map { index ->
            com.example.subscriberapp.ui.screens.solar.components.MaintenanceRecord(
                id = "record_$index",
                taskTitle = listOf("Panel Cleaning", "Inverter Inspection", "Wiring Check", "Battery Maintenance").random(),
                technician = listOf("John Smith", "Sarah Johnson", "Mike Wilson", "Lisa Brown").random(),
                scheduledDate = getDateString(-random.nextInt(days)), // random days ago
                completedDate = getDateString(-random.nextInt(days)), // random days ago
                actualDuration = getDurationString(2 + random.nextInt(4)), // 2-6 hours
                cost = 150f + random.nextFloat() * 300f,
                completedOnTime = random.nextBoolean(),
                notes = if (random.nextBoolean()) "Task completed successfully" else "",
                beforeAfterPhotos = if (random.nextBoolean()) listOf("photo1.jpg", "photo2.jpg") else emptyList()
            )
        }.sortedByDescending { it.completedDate }
    }
}

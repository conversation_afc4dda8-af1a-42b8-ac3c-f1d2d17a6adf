package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime imports - using String for dates
import java.util.*
import java.text.SimpleDateFormat

@Composable
fun MaintenanceCalendar(
    maintenanceData: MaintenanceData,
    selectedDate: String?,
    onDateSelected: (String) -> Unit,
    onTaskSelected: (MaintenanceTask) -> Unit,
    onScheduleNewTask: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val calendar = Calendar.getInstance()
    var currentMonth by remember { mutableStateOf(calendar.get(Calendar.MONTH)) }
    var currentYear by remember { mutableStateOf(calendar.get(Calendar.YEAR)) }
    
    // Simplified Maintenance Calendar - Task List View
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Maintenance Schedule",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Upcoming Tasks
        if (maintenanceData.upcomingTasks.isNotEmpty()) {
            Text(
                text = "Upcoming Tasks",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyColumn(
                modifier = Modifier.height(200.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(maintenanceData.upcomingTasks) { task ->
                    SimpleTaskCard(
                        task = task,
                        onClick = { onTaskSelected(task) }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Completed Tasks
        if (maintenanceData.completedTasks.isNotEmpty()) {
            Text(
                text = "Recently Completed",
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyColumn(
                modifier = Modifier.height(100.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(maintenanceData.completedTasks.take(3)) { task ->
                    SimpleTaskCard(
                        task = task,
                        onClick = { onTaskSelected(task) }
                    )
                }
            }
        }
    }
}

@Composable
private fun SimpleTaskCard(
    task: MaintenanceTask,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.ChevronLeft,
                contentDescription = "Previous Month",
                tint = Color.White
            )
        }
        
        Text(
            text = "${month.name.lowercase().replaceFirstChar { it.uppercase() }} $year",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        
        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = "Next Month",
                tint = Color.White
            )
        }
    }
}

@Composable
private fun CalendarGrid(
    month: Month,
    year: Int,
    maintenanceData: MaintenanceData,
    selectedDate: LocalDate?,
    onDateSelected: (LocalDate) -> Unit,
    onScheduleNewTask: (LocalDate) -> Unit
) {
    val firstDayOfMonth = LocalDate(year, month, 1)
    val lastDayOfMonth = LocalDate(year, month, month.length(isLeapYear(year)))
    val firstDayOfWeek = firstDayOfMonth.dayOfWeek.value % 7 // Convert to 0-6 (Sunday-Saturday)
    val daysInMonth = month.length(isLeapYear(year))
    
    // Get tasks for this month
    val monthTasks = maintenanceData.upcomingTasks.filter { 
        it.scheduledDate.year == year && it.scheduledDate.month == month 
    } + maintenanceData.completedTasks.filter { 
        it.scheduledDate.year == year && it.scheduledDate.month == month 
    }
    
    Column {
        // Day headers
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat").forEach { day ->
                Text(
                    text = day,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Gray,
                    modifier = Modifier.weight(1f),
                    textAlign = TextAlign.Center
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Calendar days
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.height(300.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Empty cells for days before the first day of the month
            items(firstDayOfWeek) {
                Box(modifier = Modifier.size(40.dp))
            }
            
            // Days of the month
            items(daysInMonth) { dayIndex ->
                val day = dayIndex + 1
                val date = LocalDate(year, month, day)
                val tasksForDay = monthTasks.filter { it.scheduledDate == date }
                val isSelected = selectedDate == date
                val isToday = date == Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                
                CalendarDay(
                    day = day,
                    date = date,
                    tasks = tasksForDay,
                    isSelected = isSelected,
                    isToday = isToday,
                    onDateSelected = onDateSelected,
                    onScheduleNewTask = onScheduleNewTask
                )
            }
        }
    }
}

@Composable
private fun CalendarDay(
    day: Int,
    date: LocalDate,
    tasks: List<MaintenanceTask>,
    isSelected: Boolean,
    isToday: Boolean,
    onDateSelected: (LocalDate) -> Unit,
    onScheduleNewTask: (LocalDate) -> Unit
) {
    val hasHighPriorityTask = tasks.any { it.priority == MaintenancePriority.HIGH || it.priority == MaintenancePriority.CRITICAL }
    val hasOverdueTask = tasks.any { it.status == TaskStatus.OVERDUE }
    
    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(
                when {
                    isSelected -> Color(0xFFE94560)
                    isToday -> Color(0xFF2196F3).copy(alpha = 0.3f)
                    hasOverdueTask -> Color(0xFFF44336).copy(alpha = 0.3f)
                    hasHighPriorityTask -> Color(0xFFFF9800).copy(alpha = 0.3f)
                    tasks.isNotEmpty() -> Color(0xFF4CAF50).copy(alpha = 0.3f)
                    else -> Color.Transparent
                }
            )
            .border(
                width = if (isToday) 2.dp else 0.dp,
                color = Color(0xFF2196F3),
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onDateSelected(date) },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = day.toString(),
                fontSize = 14.sp,
                fontWeight = if (isSelected || isToday) FontWeight.Bold else FontWeight.Normal,
                color = if (isSelected) Color.White else Color.White
            )
            
            if (tasks.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .background(
                            color = when {
                                hasOverdueTask -> Color(0xFFF44336)
                                hasHighPriorityTask -> Color(0xFFFF9800)
                                else -> Color(0xFF4CAF50)
                            },
                            shape = CircleShape
                        )
                )
            }
        }
    }
}

@Composable
private fun SelectedDateTasks(
    date: LocalDate,
    tasks: List<MaintenanceTask>,
    onTaskSelected: (MaintenanceTask) -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Tasks for ${date.dayOfMonth} ${date.month.name.lowercase().replaceFirstChar { it.uppercase() }}",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            if (tasks.isEmpty()) {
                Text(
                    text = "No tasks scheduled for this date",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(vertical = 16.dp)
                )
            } else {
                LazyColumn(
                    modifier = Modifier.height(200.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(tasks) { task ->
                        TaskListItem(
                            task = task,
                            onClick = { onTaskSelected(task) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TaskListItem(
    task: MaintenanceTask,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.05f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Priority indicator
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = when (task.priority) {
                            MaintenancePriority.LOW -> Color(0xFF4CAF50)
                            MaintenancePriority.MEDIUM -> Color(0xFFFF9800)
                            MaintenancePriority.HIGH -> Color(0xFFFF5722)
                            MaintenancePriority.CRITICAL -> Color(0xFFF44336)
                        },
                        shape = CircleShape
                    )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = task.title,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White
                )
                
                Text(
                    text = task.description,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    maxLines = 2
                )
                
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(top = 4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "Duration",
                        tint = Color.Gray,
                        modifier = Modifier.size(12.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(4.dp))
                    
                    Text(
                        text = "${task.estimatedDuration.inWholeHours}h ${task.estimatedDuration.inWholeMinutes % 60}m",
                        fontSize = 10.sp,
                        color = Color.Gray
                    )
                }
            }
            
            // Status indicator
            Icon(
                imageVector = when (task.status) {
                    TaskStatus.SCHEDULED -> Icons.Default.Schedule
                    TaskStatus.IN_PROGRESS -> Icons.Default.PlayArrow
                    TaskStatus.COMPLETED -> Icons.Default.CheckCircle
                    TaskStatus.CANCELLED -> Icons.Default.Cancel
                    TaskStatus.OVERDUE -> Icons.Default.Warning
                },
                contentDescription = task.status.name,
                tint = when (task.status) {
                    TaskStatus.SCHEDULED -> Color.Gray
                    TaskStatus.IN_PROGRESS -> Color(0xFF2196F3)
                    TaskStatus.COMPLETED -> Color(0xFF4CAF50)
                    TaskStatus.CANCELLED -> Color.Gray
                    TaskStatus.OVERDUE -> Color(0xFFF44336)
                },
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

// Helper function to check if a year is a leap year
private fun isLeapYear(year: Int): Boolean {
    return year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)
}

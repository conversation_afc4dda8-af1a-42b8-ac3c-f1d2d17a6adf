package com.example.subscriberapp.ui.screens.solar

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.subscriberapp.ui.components.NeonCard
import com.example.subscriberapp.ui.screens.solar.model.*
import com.example.subscriberapp.ui.screens.solar.viewmodel.SolarPanelViewModel
import com.example.subscriberapp.ui.screens.solar.components.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SolarPanelMonitoringScreen(
    onNavigateBack: () -> Unit,
    viewModel: SolarPanelViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val scrollState = rememberScrollState()
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(16.dp)
        ) {
            // Top Bar
            SolarMonitoringTopBar(
                onNavigateBack = onNavigateBack,
                onSettingsClick = { /* TODO: Settings */ }
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // MySillyDreams Solar Branding
            MySillyDreamsSolarHeader()
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // System Status Overview
            SystemStatusOverview(
                systemStatus = uiState.systemStatus,
                isLoading = uiState.isLoading
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Real-time Energy Flow Visualization
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                EnergyFlowVisualization(
                    energyFlow = uiState.energyFlow,
                    isLoading = uiState.isLoading,
                    modifier = Modifier.padding(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Battery Management Section
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                BatteryManagementSection(
                    batteryStatus = uiState.batteryStatus,
                    isLoading = uiState.isLoading,
                    modifier = Modifier.padding(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Power Generation Analytics
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                PowerGenerationAnalytics(
                    generationData = uiState.generationData,
                    isLoading = uiState.isLoading,
                    modifier = Modifier.padding(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // System Health Monitoring
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                SystemHealthMonitoring(
                    healthStatus = uiState.healthStatus,
                    isLoading = uiState.isLoading,
                    modifier = Modifier.padding(20.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(24.dp))

            // Enhanced Analytics Dashboard (Phase 3)
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                EnhancedAnalyticsDashboard(
                    historicalData = uiState.historicalData,
                    performanceMetrics = uiState.performanceMetrics,
                    isLoading = uiState.isLoading,
                    modifier = Modifier.padding(20.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Historical Data Analysis (Phase 3)
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                HistoricalDataAnalysis(
                    historicalData = uiState.historicalData,
                    selectedTimeRange = uiState.selectedTimeRange,
                    onTimeRangeChanged = { viewModel.updateTimeRange(it) },
                    onExportData = { format, timeRange ->
                        viewModel.exportData(format, timeRange)
                    },
                    exportStatus = uiState.exportStatus,
                    modifier = Modifier.padding(20.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Performance Trend Analysis (Phase 3)
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                PerformanceTrendAnalysis(
                    performanceData = uiState.performanceMetrics,
                    selectedMetric = uiState.selectedPerformanceMetric,
                    onMetricSelected = { viewModel.updateSelectedMetric(it) },
                    modifier = Modifier.padding(20.dp)
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Maintenance Management
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                if (uiState.isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp)
                            .padding(20.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = Color(0xFFE94560))
                    }
                } else {
                    MaintenanceManagement(
                        maintenanceData = uiState.maintenanceData,
                        isLoading = uiState.isLoading
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))
        }
    }
}

@Composable
private fun SolarMonitoringTopBar(
    onNavigateBack: () -> Unit,
    onSettingsClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = Color.White
                )
            }
            
            Text(
                text = "Solar Monitoring",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
        
        IconButton(onClick = onSettingsClick) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = "Settings",
                tint = Color.White
            )
        }
    }
}

@Composable
private fun MySillyDreamsSolarHeader() {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "MySillyDreams",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFFE94560)
            )
            
            Text(
                text = "Solar Panel Monitoring System",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                modifier = Modifier.padding(top = 4.dp)
            )
            
            Text(
                text = "Real-time monitoring & analytics",
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun SystemStatusOverview(
    systemStatus: SystemStatus,
    isLoading: Boolean
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(20.dp)
        ) {
            Text(
                text = "System Status",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(100.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFFE94560)
                    )
                }
            } else {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatusIndicator(
                        label = "System",
                        status = systemStatus.overallStatus,
                        value = "${systemStatus.efficiency}%"
                    )
                    
                    StatusIndicator(
                        label = "Generation",
                        status = systemStatus.generationStatus,
                        value = "${systemStatus.currentGeneration} kW"
                    )
                    
                    StatusIndicator(
                        label = "Battery",
                        status = systemStatus.batteryStatus,
                        value = "${systemStatus.batteryLevel}%"
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusIndicator(
    label: String,
    status: SystemStatusType,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(
                    color = when (status) {
                        SystemStatusType.NORMAL -> Color(0xFF4CAF50)
                        SystemStatusType.WARNING -> Color(0xFFFF9800)
                        SystemStatusType.CRITICAL -> Color(0xFFF44336)
                    },
                    shape = androidx.compose.foundation.shape.CircleShape
                )
        )
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray,
            modifier = Modifier.padding(top = 4.dp)
        )
        
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(top = 2.dp)
        )
    }
}

// Maintenance Management placeholder - will be implemented in Phase 4







// Duplicate MaintenanceManagement function removed - using the one from components package

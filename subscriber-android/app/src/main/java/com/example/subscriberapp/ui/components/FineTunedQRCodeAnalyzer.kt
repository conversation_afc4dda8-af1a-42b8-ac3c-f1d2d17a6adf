package com.example.subscriberapp.ui.components

import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Fine-tuned QR Code Analyzer using ML Kit
 * Optimized for QR code detection with enhanced accuracy and performance
 */
class FineTunedQRCodeAnalyzer(
    private val onQRCodeDetected: (String) -> Unit,
    private val onError: (Exception) -> Unit = {}
) : ImageAnalysis.Analyzer {

    private val barcodeScanner = BarcodeScanning.getClient()
    private val scope = CoroutineScope(Dispatchers.IO)
    
    // Throttling to prevent excessive processing
    private var lastAnalyzedTimestamp = 0L
    private val throttleIntervalMs = 100L // Process every 100ms max
    
    // Detection confidence and filtering
    private var consecutiveDetections = 0
    private var lastDetectedValue: String? = null
    private val requiredConsecutiveDetections = 2 // Require 2 consecutive detections for stability
    
    override fun analyze(imageProxy: ImageProxy) {
        val currentTimestamp = System.currentTimeMillis()
        
        // Throttle analysis to improve performance
        if (currentTimestamp - lastAnalyzedTimestamp < throttleIntervalMs) {
            imageProxy.close()
            return
        }
        
        lastAnalyzedTimestamp = currentTimestamp
        
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(
                mediaImage, 
                imageProxy.imageInfo.rotationDegrees
            )
            
            scope.launch {
                try {
                    barcodeScanner.process(image)
                        .addOnSuccessListener { barcodes ->
                            processBarcodes(barcodes)
                        }
                        .addOnFailureListener { exception ->
                            onError(exception)
                        }
                        .addOnCompleteListener {
                            imageProxy.close()
                        }
                } catch (e: Exception) {
                    onError(e)
                    imageProxy.close()
                }
            }
        } else {
            imageProxy.close()
        }
    }
    
    private fun processBarcodes(barcodes: List<Barcode>) {
        // Filter for QR codes only and prioritize by confidence
        val qrCodes = barcodes
            .filter { it.format == Barcode.FORMAT_QR_CODE }
            .sortedByDescending { calculateConfidence(it) }
        
        if (qrCodes.isNotEmpty()) {
            val bestQRCode = qrCodes.first()
            val rawValue = bestQRCode.rawValue
            
            if (!rawValue.isNullOrEmpty()) {
                handleDetection(rawValue)
            }
        } else {
            // Reset consecutive detection counter if no QR code found
            resetDetectionState()
        }
    }
    
    private fun handleDetection(value: String) {
        if (value == lastDetectedValue) {
            consecutiveDetections++
            
            // Only trigger callback after required consecutive detections
            if (consecutiveDetections >= requiredConsecutiveDetections) {
                onQRCodeDetected(value)
                // Reset to prevent multiple callbacks for same QR code
                resetDetectionState()
            }
        } else {
            // New QR code detected, reset counter
            lastDetectedValue = value
            consecutiveDetections = 1
        }
    }
    
    private fun resetDetectionState() {
        consecutiveDetections = 0
        lastDetectedValue = null
    }
    
    /**
     * Calculate confidence score for barcode detection
     * Higher score = better detection quality
     */
    private fun calculateConfidence(barcode: Barcode): Float {
        var confidence = 0f
        
        // Factor 1: Bounding box size (larger is generally better for QR codes)
        val boundingBox = barcode.boundingBox
        if (boundingBox != null) {
            val area = boundingBox.width() * boundingBox.height()
            confidence += (area / 10000f).coerceAtMost(1f) * 0.3f
        }
        
        // Factor 2: Corner points availability (indicates good detection)
        if (barcode.cornerPoints != null && barcode.cornerPoints!!.size == 4) {
            confidence += 0.4f
        }
        
        // Factor 3: Raw value length (reasonable QR codes have substantial content)
        val rawValue = barcode.rawValue
        if (!rawValue.isNullOrEmpty()) {
            val lengthScore = (rawValue.length / 50f).coerceAtMost(1f)
            confidence += lengthScore * 0.3f
        }
        
        return confidence.coerceIn(0f, 1f)
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            barcodeScanner.close()
        } catch (e: Exception) {
            // Ignore cleanup errors
        }
    }
}

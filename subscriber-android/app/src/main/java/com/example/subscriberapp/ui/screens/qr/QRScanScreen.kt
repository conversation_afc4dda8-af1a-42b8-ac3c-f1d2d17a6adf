package com.example.subscriberapp.ui.screens.qr

import android.Manifest
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.QrCode
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.services.CheckInResponse
import com.example.subscriberapp.services.CheckInResult
import com.example.subscriberapp.services.CyclingResultsManager
import com.example.subscriberapp.ui.components.GradientButton
import com.example.subscriberapp.ui.components.NeonCard
import com.example.subscriberapp.ui.components.RealCameraPreviewWithMLKit
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun QRScanScreen(
    onNavigateBack: () -> Unit,
    onNavigateToSuccess: () -> Unit = {},
    onNavigateToError: () -> Unit = {}
) {
    val context = LocalContext.current
    val cyclingManager = remember { CyclingResultsManager(context) }
    val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)
    
    var scanState by remember { mutableStateOf(QRScanState.READY) }
    var scanResult by remember { mutableStateOf<CheckInResponse?>(null) }
    var detectedQRCode by remember { mutableStateOf<String?>(null) }
    var cameraError by remember { mutableStateOf<String?>(null) }
    var shouldNavigateToSuccess by remember { mutableStateOf(true) } // Alternates between success and error
    
    // Handle QR code detection from real camera
    val onQRCodeDetected = { qrCode: String ->
        if (scanState == QRScanState.SCANNING) {
            detectedQRCode = qrCode
            // Use cycling manager to get result based on detected QR code
            val result = cyclingManager.getQRResult()
            scanResult = result
            scanState = QRScanState.RESULT
        }
    }
    
    LaunchedEffect(scanState) {
        when (scanState) {
            QRScanState.RESULT -> {
                delay(3000) // Show result for 3 seconds
                scanState = QRScanState.READY
                scanResult = null
                detectedQRCode = null
            }
            else -> {}
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "QR Code Scanner",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Main Content
            when {
                !cameraPermissionState.status.isGranted -> {
                    CameraPermissionContent(
                        permissionState = cameraPermissionState,
                        onRequestPermission = { cameraPermissionState.launchPermissionRequest() }
                    )
                }
                scanState == QRScanState.READY -> {
                    QRReadyContent(
                        onStartScan = { scanState = QRScanState.SCANNING }
                    )
                }
                scanState == QRScanState.SCANNING -> {
                    QRScanningWithCameraContent(
                        onQRCodeDetected = onQRCodeDetected,
                        onStopScan = {
                            cameraError = null
                            scanState = QRScanState.READY
                        },
                        cameraError = cameraError,
                        onCameraError = { error -> cameraError = error }
                    )
                }
                scanState == QRScanState.RESULT -> {
                    scanResult?.let { result ->
                        QRResultContent(
                            result = result,
                            detectedQRCode = detectedQRCode
                        )
                    }
                }
            }
        }
    }
}

enum class QRScanState {
    READY, SCANNING, RESULT
}

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun CameraPermissionContent(
    permissionState: com.google.accompanist.permissions.PermissionState,
    onRequestPermission: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.QrCode,
                    contentDescription = "Camera Permission",
                    tint = Color(0xFFE94560),
                    modifier = Modifier.size(80.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Camera Permission Required",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = if (permissionState.status.shouldShowRationale) {
                        "Camera access is needed to scan QR codes. Please grant permission to continue."
                    } else {
                        "To scan QR codes, we need access to your camera."
                    },
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                GradientButton(
                    text = "Grant Camera Permission",
                    onClick = onRequestPermission,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun QRReadyContent(onStartScan: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.QrCode,
                    contentDescription = "QR Code",
                    tint = Color(0xFFE94560),
                    modifier = Modifier.size(80.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Ready to Scan",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "Position the QR code within the camera view",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(32.dp))
                
                GradientButton(
                    text = "Start Scanning",
                    onClick = onStartScan,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun QRScanningWithCameraContent(
    onQRCodeDetected: (String) -> Unit,
    onStopScan: () -> Unit,
    cameraError: String?,
    onCameraError: (String) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Real camera preview with ML Kit
                RealCameraPreviewWithMLKit(
                    modifier = Modifier
                        .size(280.dp)
                        .clip(RoundedCornerShape(16.dp)),
                    onQRCodeDetected = onQRCodeDetected,
                    onError = { error ->
                        // Handle camera errors - could show error message
                        android.util.Log.e("QRScanScreen", "Camera error: $error")
                        println("Camera error: $error")
                        onCameraError(error)
                    },
                    isScanning = true
                )
                
                Spacer(modifier = Modifier.height(24.dp))

                Text(
                    text = "Scanning for QR Code...",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )

                Text(
                    text = "Point your camera at a QR code",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )

                // Show camera error if any
                cameraError?.let { error ->
                    Text(
                        text = "Camera Error: $error",
                        fontSize = 12.sp,
                        color = Color(0xFFE94560),
                        modifier = Modifier.padding(top = 8.dp),
                        textAlign = TextAlign.Center
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                GradientButton(
                    text = "Stop Scanning",
                    onClick = onStopScan,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun QRResultContent(
    result: CheckInResponse,
    detectedQRCode: String?
) {
    val resultColor = when (result.result) {
        CheckInResult.SUCCESS -> Color(0xFF4CAF50)
        CheckInResult.EXPIRED -> Color(0xFFFF9800)
        CheckInResult.INVALID -> Color(0xFFF44336)
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = resultColor
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = result.icon,
                    fontSize = 64.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = result.title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = result.message,
                    fontSize = 16.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                // Show detected QR code content (optional)
                detectedQRCode?.let { qrCode ->
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Detected: ${qrCode.take(50)}${if (qrCode.length > 50) "..." else ""}",
                        fontSize = 12.sp,
                        color = Color.Gray,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }
        }
    }
}

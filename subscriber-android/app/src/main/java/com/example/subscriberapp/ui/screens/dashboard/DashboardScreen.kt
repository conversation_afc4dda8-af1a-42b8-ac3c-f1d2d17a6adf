package com.example.subscriberapp.ui.screens.dashboard

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.subscriberapp.services.MockAuthService
import com.example.subscriberapp.ui.components.NeonCard
import java.text.SimpleDateFormat
import java.util.*

data class DashboardItem(
    val title: String,
    val subtitle: String,
    val icon: ImageVector,
    val colors: List<Color>,
    val onClick: () -> Unit
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    onNavigateToQR: () -> Unit,
    onNavigateToNFC: () -> Unit,
    onNavigateToWiFi: () -> Unit,
    onNavigateToBluetooth: () -> Unit,
    onNavigateToIOT: () -> Unit,
    onNavigateToSolar: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToHistory: () -> Unit,
    authService: MockAuthService = viewModel()
) {
    val currentTime = remember {
        SimpleDateFormat("EEEE, MMMM dd, yyyy\nHH:mm:ss", Locale.getDefault()).format(Date())
    }
    
    val dashboardItems = listOf(
        DashboardItem(
            title = "QR Scan",
            subtitle = "Scan QR codes",
            icon = Icons.Default.QrCode,
            colors = listOf(Color(0xFFE94560), Color(0xFFFF6B9D)),
            onClick = onNavigateToQR
        ),
        DashboardItem(
            title = "NFC Check-in",
            subtitle = "Tap NFC card",
            icon = Icons.Default.Nfc,
            colors = listOf(Color(0xFF4ECDC4), Color(0xFF44A08D)),
            onClick = onNavigateToNFC
        ),
        DashboardItem(
            title = "WiFi Check-in",
            subtitle = "Connect via WiFi",
            icon = Icons.Default.Wifi,
            colors = listOf(Color(0xFF667eea), Color(0xFF764ba2)),
            onClick = onNavigateToWiFi
        ),
        DashboardItem(
            title = "Bluetooth",
            subtitle = "Bluetooth beacon",
            icon = Icons.Default.Bluetooth,
            colors = listOf(Color(0xFF667eea), Color(0xFF764ba2)),
            onClick = onNavigateToBluetooth
        ),
        DashboardItem(
            title = "IOT Control",
            subtitle = "Smart home devices",
            icon = Icons.Default.Home,
            colors = listOf(Color(0xFFf093fb), Color(0xFFf5576c)),
            onClick = onNavigateToIOT
        ),
        DashboardItem(
            title = "Solar Monitoring",
            subtitle = "Solar panel system",
            icon = Icons.Default.WbSunny,
            colors = listOf(Color(0xFFFFD700), Color(0xFFFF8C00)),
            onClick = onNavigateToSolar
        ),
        DashboardItem(
            title = "Profile",
            subtitle = "User settings",
            icon = Icons.Default.Person,
            colors = listOf(Color(0xFF4facfe), Color(0xFF00f2fe)),
            onClick = onNavigateToProfile
        ),
        DashboardItem(
            title = "History",
            subtitle = "Attendance records",
            icon = Icons.Default.History,
            colors = listOf(Color(0xFF43e97b), Color(0xFF38f9d7)),
            onClick = onNavigateToHistory
        )
    )
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Welcome, ${authService.getCurrentUserDisplayName()}!",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )
                    
                    Text(
                        text = currentTime,
                        fontSize = 14.sp,
                        color = Color(0xFFE94560),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(top = 8.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Dashboard Grid
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(dashboardItems) { item ->
                    DashboardCard(item = item)
                }
            }
        }
    }
}

@Composable
fun DashboardCard(item: DashboardItem) {
    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .clip(RoundedCornerShape(16.dp))
            .background(
                brush = Brush.verticalGradient(item.colors)
            )
            .clickable { item.onClick() },
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = item.icon,
                contentDescription = item.title,
                tint = Color.White,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = item.title,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = item.subtitle,
                color = Color.White.copy(alpha = 0.8f),
                fontSize = 12.sp,
                textAlign = TextAlign.Center
            )
        }
    }
}

package com.example.subscriberapp.ui.screens.solar.model

import java.util.Date
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * Main UI State for Solar Panel Monitoring Screen
 */
data class SolarPanelUiState(
    val isLoading: Boolean = false,
    val systemStatus: SystemStatus = SystemStatus(),
    val energyFlow: EnergyFlowData = EnergyFlowData(),
    val batteryStatus: BatteryStatus = BatteryStatus(),
    val generationData: PowerGenerationData = PowerGenerationData(),
    val healthStatus: SystemHealthStatus = SystemHealthStatus(),
    val maintenanceData: MaintenanceData = MaintenanceData(),
    // Phase 3: Enhanced Analytics Properties
    val historicalData: List<HistoricalDataSet> = emptyList(),
    val performanceMetrics: List<PerformanceMetric> = emptyList(),
    val selectedTimeRange: TimeRange = TimeRange.DAY,
    val selectedPerformanceMetric: PerformanceMetricType = PerformanceMetricType.GENERATION,
    val exportStatus: ExportStatus? = null,
    val error: String? = null
)

/**
 * System Status Overview
 */
data class SystemStatus(
    val overallStatus: SystemStatusType = SystemStatusType.NORMAL,
    val efficiency: Float = 0f,
    val generationStatus: SystemStatusType = SystemStatusType.NORMAL,
    val currentGeneration: Float = 0f,
    val batteryStatus: SystemStatusType = SystemStatusType.NORMAL,
    val batteryLevel: Float = 0f,
    val lastUpdated: String? = null // Changed from LocalDateTime to String
)

enum class SystemStatusType {
    NORMAL, WARNING, CRITICAL
}

/**
 * Energy Flow Visualization Data
 */
data class EnergyFlowData(
    val currentGeneration: Float = 0f,
    val voltage: Float = 0f,
    val current: Float = 0f,
    val routingMode: EnergyRoutingMode = EnergyRoutingMode.HOUSEHOLD_DIRECT,
    val flowRate: Float = 0f,
    val powerToHousehold: Float = 0f,
    val powerToBattery: Float = 0f,
    val powerFromGrid: Float = 0f
)

enum class EnergyRoutingMode {
    HOUSEHOLD_DIRECT,    // Solar → Inverter → Household
    BATTERY_CHARGING,    // Solar → Inverter → Battery
    BATTERY_DISCHARGING, // Battery → Household
    GRID_FEEDING,        // Solar → Grid (excess power)
    HYBRID              // Multiple routes active
}

/**
 * Battery Management Data
 */
data class BatteryStatus(
    val chargePercentage: Float = 0f,
    val healthPercentage: Float = 100f,
    val isCharging: Boolean = false,
    val chargeRate: Float = 0f, // kW
    val dischargeRate: Float = 0f, // kW
    val temperature: Float = 25f, // Celsius
    val remainingCapacity: Float = 0f, // kWh
    val totalCapacity: Float = 10f, // kWh
    val estimatedTimeToFull: String? = null, // Changed from Duration to String
    val estimatedTimeToEmpty: String? = null, // Changed from Duration to String
    val cycleCount: Int = 0,
    val lastMaintenanceDate: String? = null // Changed from LocalDate to String
)

/**
 * Power Generation Analytics Data
 */
data class PowerGenerationData(
    val todayGeneration: List<HourlyGenerationData> = emptyList(),
    val monthlyGeneration: List<DailyGenerationData> = emptyList(),
    val totalGeneratedToday: Float = 0f, // kWh
    val totalGeneratedThisMonth: Float = 0f, // kWh
    val totalGeneratedAllTime: Float = 0f, // kWh
    val peakGenerationToday: Float = 0f, // kW
    val averageEfficiency: Float = 0f, // %
    val costSavingsToday: Float = 0f, // Currency
    val costSavingsThisMonth: Float = 0f, // Currency
    val co2SavedToday: Float = 0f, // kg
    val weatherImpact: WeatherImpact = WeatherImpact()
)

data class HourlyGenerationData(
    val hour: Int, // 0-23
    val powerGenerated: Float, // kWh
    val efficiency: Float, // %
    val weatherCondition: WeatherCondition = WeatherCondition.SUNNY,
    val temperature: Float = 25f // Celsius
)

data class DailyGenerationData(
    val date: String, // Changed from LocalDate to String for Android compatibility
    val totalGenerated: Float, // kWh
    val peakGeneration: Float, // kW
    val averageEfficiency: Float, // %
    val weatherCondition: WeatherCondition = WeatherCondition.SUNNY
)

data class WeatherImpact(
    val currentCondition: WeatherCondition = WeatherCondition.SUNNY,
    val temperature: Float = 25f,
    val cloudCover: Float = 0f, // 0-100%
    val expectedImpact: Float = 0f // -100 to +100%
)

enum class WeatherCondition {
    SUNNY, PARTLY_CLOUDY, CLOUDY, RAINY, STORMY
}

/**
 * System Health Monitoring Data
 */
data class SystemHealthStatus(
    val overallHealth: Float = 100f, // %
    val solarPanels: List<PanelStatus> = emptyList(),
    val inverterStatus: InverterStatus = InverterStatus(),
    val wiringStatus: WiringStatus = WiringStatus(),
    val meterStatus: MeterStatus = MeterStatus(),
    val alerts: List<SystemAlert> = emptyList(),
    val lastHealthCheck: String? = null // Changed from LocalDateTime to String
)

data class PanelStatus(
    val id: String,
    val position: PanelPosition,
    val health: Float = 100f, // %
    val currentOutput: Float = 0f, // W
    val maxOutput: Float = 300f, // W
    val temperature: Float = 25f, // Celsius
    val status: ComponentStatus = ComponentStatus.NORMAL,
    val lastCleaned: String? = null // Changed from LocalDate to String
)

data class PanelPosition(
    val row: Int,
    val column: Int,
    val orientation: String = "South" // N, S, E, W, etc.
)

data class InverterStatus(
    val id: String = "INV001",
    val health: Float = 100f, // %
    val efficiency: Float = 95f, // %
    val temperature: Float = 45f, // Celsius
    val status: ComponentStatus = ComponentStatus.NORMAL,
    val inputVoltage: Float = 400f, // V
    val outputVoltage: Float = 230f, // V
    val frequency: Float = 50f, // Hz
    val lastMaintenance: String? = null // Changed from LocalDate to String
)

data class WiringStatus(
    val overallHealth: Float = 100f, // %
    val dcWiring: ComponentStatus = ComponentStatus.NORMAL,
    val acWiring: ComponentStatus = ComponentStatus.NORMAL,
    val groundingStatus: ComponentStatus = ComponentStatus.NORMAL,
    val connectionIntegrity: Float = 100f, // %
    val lastInspection: String? = null // Changed from LocalDate to String
)

data class MeterStatus(
    val isConnected: Boolean = true,
    val communicationStatus: ComponentStatus = ComponentStatus.NORMAL,
    val dataAccuracy: Float = 100f, // %
    val lastReading: String? = null, // Changed from LocalDateTime to String
    val totalEnergyExported: Float = 0f, // kWh
    val totalEnergyImported: Float = 0f // kWh
)

enum class ComponentStatus {
    NORMAL, WARNING, CRITICAL, OFFLINE
}

data class SystemAlert(
    val id: String,
    val title: String,
    val description: String,
    val severity: AlertSeverity,
    val component: String,
    val timestamp: String, // Changed from LocalDateTime to String
    val isResolved: Boolean = false,
    val actionRequired: String? = null
)

enum class AlertSeverity {
    INFO, WARNING, CRITICAL
}

/**
 * Maintenance Management Data
 */
data class MaintenanceData(
    val upcomingTasks: List<MaintenanceTask> = emptyList(),
    val completedTasks: List<MaintenanceTask> = emptyList(),
    val overdueTasks: List<MaintenanceTask> = emptyList(),
    val nextScheduledMaintenance: String? = null, // Changed from LocalDate to String
    val totalMaintenanceCost: Float = 0f,
    val maintenanceHistory: List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceRecord> = emptyList(),
    val notifications: List<com.example.subscriberapp.ui.screens.solar.components.MaintenanceNotification> = emptyList()
)

data class MaintenanceTask(
    val id: String,
    val title: String,
    val description: String,
    val scheduledDate: String, // Changed from LocalDate to String
    val priority: MaintenancePriority,
    val estimatedDuration: String, // Changed from Duration to String
    val assignedTechnician: String? = null,
    val checklist: List<ChecklistItem> = emptyList(),
    val status: TaskStatus,
    val cost: Float = 0f,
    val components: List<String> = emptyList()
)

data class ChecklistItem(
    val id: String,
    val description: String,
    val isCompleted: Boolean = false,
    val notes: String? = null,
    val photoRequired: Boolean = false,
    val photoUrl: String? = null
)

// MaintenanceRecord moved to components package

enum class MaintenancePriority {
    LOW, MEDIUM, HIGH, CRITICAL
}

enum class TaskStatus {
    SCHEDULED, IN_PROGRESS, COMPLETED, CANCELLED, OVERDUE
}

/**
 * Chart and Analytics Data Types
 */
data class ChartDataPoint(
    val timestamp: String, // Changed from LocalDateTime to String
    val value: Float,
    val label: String? = null
)

data class GenerationDataPoint(
    val timestamp: String, // Changed from LocalDateTime to String
    val powerGenerated: Float,
    val efficiency: Float,
    val weatherCondition: WeatherCondition
)

enum class ChartType {
    LINE, BAR, PIE, AREA
}

enum class TimeRange {
    HOUR, DAY, WEEK, MONTH, QUARTER, YEAR, ALL_TIME
}

/**
 * Settings and Configuration
 */
data class SolarSystemConfiguration(
    val systemId: String,
    val installationDate: String, // Changed from LocalDate to String
    val totalPanelCount: Int,
    val totalCapacity: Float, // kW
    val batteryCapacity: Float, // kWh
    val inverterModel: String,
    val panelModel: String,
    val location: SystemLocation,
    val electricityRate: Float, // per kWh
    val feedInTariff: Float, // per kWh
    val currency: String = "USD"
)

data class SystemLocation(
    val latitude: Double,
    val longitude: Double,
    val address: String,
    val timezone: String
)

/**
 * Phase 3: Enhanced Analytics Data Types
 */
data class HistoricalDataSet(
    val date: String, // Changed from LocalDate to String
    val totalGeneration: Float,
    val peakGeneration: Float,
    val averageEfficiency: Float,
    val weatherCondition: WeatherCondition
)

data class PerformanceMetric(
    val type: PerformanceMetricType,
    val timestamp: String, // Changed from LocalDateTime to String
    val value: Float,
    val unit: String,
    val confidenceLevel: Float = 95f // %
)

enum class PerformanceMetricType(val displayName: String) {
    GENERATION("Power Generation"),
    EFFICIENCY("System Efficiency"),
    BATTERY_HEALTH("Battery Health"),
    INVERTER_PERFORMANCE("Inverter Performance"),
    WEATHER_IMPACT("Weather Impact"),
    COST_SAVINGS("Cost Savings")
}

data class ExportStatus(
    val isExporting: Boolean = false,
    val format: ExportFormat? = null,
    val progress: Float = 0f, // 0-100%
    val filePath: String? = null,
    val error: String? = null
)

enum class ExportFormat(val displayName: String, val extension: String) {
    CSV("CSV", "csv"),
    JSON("JSON", "json"),
    PDF("PDF", "pdf"),
    EXCEL("Excel", "xlsx")
}

enum class TrendDirection {
    UP, DOWN, STABLE
}

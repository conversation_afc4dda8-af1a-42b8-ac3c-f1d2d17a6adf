package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime import - using String for dates
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

@Composable
fun MaintenanceNotifications(
    notifications: List<MaintenanceNotification>,
    onNotificationClick: (MaintenanceNotification) -> Unit,
    onNotificationDismiss: (String) -> Unit,
    onSnoozeNotification: (String, Duration) -> Unit,
    onMarkAllAsRead: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Notifications Header
        NotificationsHeader(
            unreadCount = notifications.count { !it.isRead },
            onMarkAllAsRead = onMarkAllAsRead
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Notifications List
        if (notifications.isEmpty()) {
            EmptyNotificationsState()
        } else {
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(notifications) { notification ->
                    NotificationCard(
                        notification = notification,
                        onClick = { onNotificationClick(notification) },
                        onDismiss = { onNotificationDismiss(notification.id) },
                        onSnooze = { duration -> onSnoozeNotification(notification.id, duration) }
                    )
                }
            }
        }
    }
}

@Composable
private fun NotificationsHeader(
    unreadCount: Int,
    onMarkAllAsRead: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = "Maintenance Notifications",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            if (unreadCount > 0) {
                Text(
                    text = "$unreadCount unread notifications",
                    fontSize = 12.sp,
                    color = Color(0xFFE94560)
                )
            } else {
                Text(
                    text = "All notifications read",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
        
        if (unreadCount > 0) {
            TextButton(
                onClick = onMarkAllAsRead
            ) {
                Icon(
                    imageVector = Icons.Default.DoneAll,
                    contentDescription = "Mark all as read",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("Mark all read", fontSize = 12.sp)
            }
        }
    }
}

@Composable
private fun NotificationCard(
    notification: MaintenanceNotification,
    onClick: () -> Unit,
    onDismiss: () -> Unit,
    onSnooze: (Duration) -> Unit
) {
    var showSnoozeOptions by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (notification.isRead) 
                Color.Black.copy(alpha = 0.2f) 
            else 
                Color.Black.copy(alpha = 0.4f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Notification Icon
                NotificationIcon(
                    type = notification.type,
                    priority = notification.priority
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                // Notification Content
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = notification.title,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    
                    Text(
                        text = notification.message,
                        fontSize = 12.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(top = 2.dp)
                    )
                    
                    // Time and Task Info
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(top = 4.dp)
                    ) {
                        Text(
                            text = formatNotificationTime(notification.scheduledTime),
                            fontSize = 10.sp,
                            color = Color.Gray
                        )
                        
                        if (notification.taskId != null) {
                            Text(
                                text = " • Task ID: ${notification.taskId}",
                                fontSize = 10.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
                
                // Unread Indicator
                if (!notification.isRead) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = Color(0xFFE94560),
                                shape = CircleShape
                            )
                    )
                }
            }
            
            // Action Buttons
            if (!notification.isRead) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Snooze Button
                    OutlinedButton(
                        onClick = { showSnoozeOptions = !showSnoozeOptions },
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Snooze,
                            contentDescription = "Snooze",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Snooze", fontSize = 12.sp)
                    }
                    
                    // Dismiss Button
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Dismiss",
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Dismiss", fontSize = 12.sp)
                    }
                }
                
                // Snooze Options
                if (showSnoozeOptions) {
                    Spacer(modifier = Modifier.height(8.dp))
                    SnoozeOptions(
                        onSnooze = { duration ->
                            onSnooze(duration)
                            showSnoozeOptions = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun NotificationIcon(
    type: NotificationType,
    priority: NotificationPriority
) {
    val (icon, backgroundColor) = when (type) {
        NotificationType.MAINTENANCE_DUE -> Icons.Default.Build to when (priority) {
            NotificationPriority.LOW -> Color(0xFF4CAF50)
            NotificationPriority.MEDIUM -> Color(0xFFFF9800)
            NotificationPriority.HIGH -> Color(0xFFFF5722)
            NotificationPriority.CRITICAL -> Color(0xFFF44336)
        }
        NotificationType.SYSTEM_ALERT -> Icons.Default.Warning to Color(0xFFFF9800)
        NotificationType.TASK_REMINDER -> Icons.Default.Schedule to Color(0xFF2196F3)
        NotificationType.PERFORMANCE_ISSUE -> Icons.Default.TrendingDown to Color(0xFFF44336)
    }
    
    Box(
        modifier = Modifier
            .size(40.dp)
            .background(
                color = backgroundColor.copy(alpha = 0.2f),
                shape = CircleShape
            ),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = type.name,
            tint = backgroundColor,
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun SnoozeOptions(
    onSnooze: (Duration) -> Unit
) {
    val snoozeOptions = listOf(
        "15 minutes" to 15.minutes,
        "1 hour" to 1.hours,
        "4 hours" to 4.hours,
        "1 day" to 1.days
    )
    
    Column {
        Text(
            text = "Snooze for:",
            fontSize = 12.sp,
            color = Color.Gray,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            snoozeOptions.forEach { (label, duration) ->
                FilterChip(
                    onClick = { onSnooze(duration) },
                    label = { Text(label, fontSize = 10.sp) },
                    selected = false,
                    colors = FilterChipDefaults.filterChipColors(
                        containerColor = Color.White.copy(alpha = 0.1f),
                        labelColor = Color.White
                    )
                )
            }
        }
    }
}

@Composable
private fun EmptyNotificationsState() {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.NotificationsNone,
                contentDescription = "No notifications",
                tint = Color.Gray,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "No Notifications",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Text(
                text = "You're all caught up! No maintenance notifications at this time.",
                fontSize = 12.sp,
                color = Color.Gray,
                textAlign = androidx.compose.ui.text.style.TextAlign.Center,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
    }
}

// Data classes for notifications
data class MaintenanceNotification(
    val id: String,
    val type: NotificationType,
    val priority: NotificationPriority,
    val title: String,
    val message: String,
    val scheduledTime: String, // Changed from LocalDateTime to String
    val taskId: String? = null,
    val isRead: Boolean = false,
    val isSnoozed: Boolean = false,
    val snoozeUntil: String? = null // Changed from LocalDateTime to String
)

enum class NotificationType {
    MAINTENANCE_DUE,
    SYSTEM_ALERT,
    TASK_REMINDER,
    PERFORMANCE_ISSUE
}

enum class NotificationPriority {
    LOW, MEDIUM, HIGH, CRITICAL
}

// Helper functions
private fun formatNotificationTime(time: String): String {
    // Simplified - time is already a formatted string
    return time
}

// Notification Management Component
@Composable
fun NotificationSettings(
    settings: NotificationSettings,
    onSettingsChange: (NotificationSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp),
        modifier = modifier
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Notification Settings",
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            // Enable/Disable Notifications
            SettingRow(
                title = "Enable Notifications",
                description = "Receive maintenance reminders and alerts",
                isEnabled = settings.notificationsEnabled,
                onToggle = { enabled ->
                    onSettingsChange(settings.copy(notificationsEnabled = enabled))
                }
            )
            
            if (settings.notificationsEnabled) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // Notification Types
                Text(
                    text = "Notification Types",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                SettingRow(
                    title = "Maintenance Due",
                    description = "Upcoming maintenance tasks",
                    isEnabled = settings.maintenanceDueEnabled,
                    onToggle = { enabled ->
                        onSettingsChange(settings.copy(maintenanceDueEnabled = enabled))
                    }
                )
                
                SettingRow(
                    title = "System Alerts",
                    description = "Critical system issues",
                    isEnabled = settings.systemAlertsEnabled,
                    onToggle = { enabled ->
                        onSettingsChange(settings.copy(systemAlertsEnabled = enabled))
                    }
                )
                
                SettingRow(
                    title = "Performance Issues",
                    description = "System performance problems",
                    isEnabled = settings.performanceIssuesEnabled,
                    onToggle = { enabled ->
                        onSettingsChange(settings.copy(performanceIssuesEnabled = enabled))
                    }
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Reminder Timing
                Text(
                    text = "Reminder Timing",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Remind me ${settings.reminderDays} days before",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    
                    // Simple day selector (could be enhanced with a slider)
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        listOf(1, 3, 7, 14).forEach { days ->
                            FilterChip(
                                onClick = { 
                                    onSettingsChange(settings.copy(reminderDays = days))
                                },
                                label = { Text("${days}d", fontSize = 10.sp) },
                                selected = settings.reminderDays == days,
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = Color(0xFFE94560),
                                    selectedLabelColor = Color.White,
                                    containerColor = Color.White.copy(alpha = 0.1f),
                                    labelColor = Color.Gray
                                )
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun SettingRow(
    title: String,
    description: String,
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
            Text(
                text = description,
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
        
        Switch(
            checked = isEnabled,
            onCheckedChange = onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = Color(0xFFE94560),
                uncheckedThumbColor = Color.Gray,
                uncheckedTrackColor = Color.Gray.copy(alpha = 0.3f)
            )
        )
    }
}

data class NotificationSettings(
    val notificationsEnabled: Boolean = true,
    val maintenanceDueEnabled: Boolean = true,
    val systemAlertsEnabled: Boolean = true,
    val performanceIssuesEnabled: Boolean = true,
    val reminderDays: Int = 7
)

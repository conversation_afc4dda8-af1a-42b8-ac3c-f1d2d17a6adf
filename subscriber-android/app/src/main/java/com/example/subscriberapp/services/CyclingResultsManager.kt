package com.example.subscriberapp.services

import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.ViewModel

enum class CheckInResult {
    SUCCESS,
    EXPIRED,
    INVALID
}

data class CheckInResponse(
    val result: CheckInResult,
    val title: String,
    val message: String,
    val icon: String
)

class CyclingResultsManager(private val context: Context) : ViewModel() {
    private val prefs: SharedPreferences = context.getSharedPreferences("cycling_results", Context.MODE_PRIVATE)
    
    private fun getNextResult(key: String): CheckInResult {
        val currentCount = prefs.getInt(key, 0)
        val nextResult = when (currentCount % 3) {
            0 -> CheckInResult.SUCCESS
            1 -> CheckInResult.EXPIRED
            else -> CheckInResult.INVALID
        }
        
        // Update counter
        prefs.edit().putInt(key, currentCount + 1).apply()
        
        return nextResult
    }
    
    fun getQRResult(): CheckInResponse {
        val result = getNextResult("qr_count")
        return when (result) {
            CheckInResult.SUCCESS -> CheckInResponse(
                result = result,
                title = "QR Scan Successful!",
                message = "You have been checked in successfully.",
                icon = "✅"
            )
            CheckInResult.EXPIRED -> CheckInResponse(
                result = result,
                title = "QR Code Expired",
                message = "This QR code has expired. Please get a new one.",
                icon = "⏰"
            )
            CheckInResult.INVALID -> CheckInResponse(
                result = result,
                title = "Invalid QR Code",
                message = "This QR code is not valid for check-in.",
                icon = "❌"
            )
        }
    }
    
    fun getNFCResult(): CheckInResponse {
        val result = getNextResult("nfc_count")
        return when (result) {
            CheckInResult.SUCCESS -> CheckInResponse(
                result = result,
                title = "NFC Check-in Successful!",
                message = "NFC card detected and verified.",
                icon = "📱"
            )
            CheckInResult.EXPIRED -> CheckInResponse(
                result = result,
                title = "NFC Card Expired",
                message = "Your NFC card has expired. Please contact admin.",
                icon = "⏰"
            )
            CheckInResult.INVALID -> CheckInResponse(
                result = result,
                title = "Invalid NFC Card",
                message = "This NFC card is not registered.",
                icon = "❌"
            )
        }
    }
    
    fun getWiFiResult(): CheckInResponse {
        val result = getNextResult("wifi_count")
        return when (result) {
            CheckInResult.SUCCESS -> CheckInResponse(
                result = result,
                title = "WiFi Check-in Successful!",
                message = "Connected to secure network and verified.",
                icon = "📶"
            )
            CheckInResult.EXPIRED -> CheckInResponse(
                result = result,
                title = "WiFi Session Expired",
                message = "Your WiFi session has expired.",
                icon = "⏰"
            )
            CheckInResult.INVALID -> CheckInResponse(
                result = result,
                title = "WiFi Connection Failed",
                message = "Unable to connect to the secure network.",
                icon = "❌"
            )
        }
    }
    
    fun getBluetoothResult(): CheckInResponse {
        val result = getNextResult("bluetooth_count")
        return when (result) {
            CheckInResult.SUCCESS -> CheckInResponse(
                result = result,
                title = "Bluetooth Check-in Successful!",
                message = "Bluetooth beacon detected and verified.",
                icon = "🔵"
            )
            CheckInResult.EXPIRED -> CheckInResponse(
                result = result,
                title = "Bluetooth Session Expired",
                message = "Your Bluetooth session has expired.",
                icon = "⏰"
            )
            CheckInResult.INVALID -> CheckInResponse(
                result = result,
                title = "Bluetooth Connection Failed",
                message = "Unable to detect the required beacon.",
                icon = "❌"
            )
        }
    }
}

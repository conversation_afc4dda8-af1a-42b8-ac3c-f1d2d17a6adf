package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
import kotlin.math.*

@Composable
fun PerformanceTrendAnalysis(
    performanceData: List<PerformanceMetric>,
    selectedMetric: PerformanceMetricType,
    onMetricSelected: (PerformanceMetricType) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Performance Trend Analysis",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // Metric Selector
        PerformanceMetricSelector(
            selectedMetric = selectedMetric,
            onMetricSelected = onMetricSelected
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Trend Visualization
        TrendVisualizationCard(
            performanceData = performanceData,
            selectedMetric = selectedMetric
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Performance Insights
        PerformanceInsightsSection(
            performanceData = performanceData,
            selectedMetric = selectedMetric
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Seasonal Analysis
        SeasonalAnalysisSection(
            performanceData = performanceData
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Predictive Analysis
        PredictiveAnalysisSection(
            performanceData = performanceData,
            selectedMetric = selectedMetric
        )
    }
}

@Composable
private fun PerformanceMetricSelector(
    selectedMetric: PerformanceMetricType,
    onMetricSelected: (PerformanceMetricType) -> Unit
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(PerformanceMetricType.values()) { metric ->
            FilterChip(
                onClick = { onMetricSelected(metric) },
                label = { Text(metric.displayName) },
                selected = selectedMetric == metric,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = Color(0xFFE94560),
                    selectedLabelColor = Color.White,
                    containerColor = Color.Black.copy(alpha = 0.3f),
                    labelColor = Color.Gray
                ),
                leadingIcon = {
                    Icon(
                        imageVector = getMetricIcon(metric),
                        contentDescription = metric.displayName,
                        modifier = Modifier.size(16.dp)
                    )
                }
            )
        }
    }
}

@Composable
private fun TrendVisualizationCard(
    performanceData: List<PerformanceMetric>,
    selectedMetric: PerformanceMetricType
) {
    val filteredData = performanceData.filter { it.type == selectedMetric }
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "${selectedMetric.displayName} Trend",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            if (filteredData.isNotEmpty()) {
                TrendChart(
                    data = filteredData,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available for ${selectedMetric.displayName}",
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

@Composable
private fun TrendChart(
    data: List<PerformanceMetric>,
    modifier: Modifier = Modifier
) {
    val animatedProgress by animateFloatAsState(
        targetValue = 1f,
        animationSpec = tween(2000, easing = FastOutSlowInEasing),
        label = "trend_chart_animation"
    )
    
    Canvas(
        modifier = modifier
    ) {
        drawTrendChart(data, animatedProgress)
    }
}

private fun DrawScope.drawTrendChart(
    data: List<PerformanceMetric>,
    animatedProgress: Float
) {
    if (data.isEmpty()) return
    
    val chartWidth = size.width - 60.dp.toPx()
    val chartHeight = size.height - 60.dp.toPx()
    val chartLeft = 30.dp.toPx()
    val chartTop = 30.dp.toPx()
    
    val maxValue = data.maxOfOrNull { it.value } ?: 1f
    val minValue = data.minOfOrNull { it.value } ?: 0f
    val valueRange = maxValue - minValue
    
    val pointSpacing = chartWidth / (data.size - 1).coerceAtLeast(1)
    
    // Draw grid
    drawTrendGrid(chartLeft, chartTop, chartWidth, chartHeight, maxValue, minValue)
    
    // Create trend line path
    val trendPath = Path()
    val points = mutableListOf<Offset>()
    
    data.forEachIndexed { index, metric ->
        val x = chartLeft + index * pointSpacing
        val y = chartTop + chartHeight - ((metric.value - minValue) / valueRange) * chartHeight
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            trendPath.moveTo(x, y)
        } else {
            trendPath.lineTo(x, y)
        }
    }
    
    // Draw trend area
    val areaPath = Path().apply {
        addPath(trendPath)
        lineTo(points.last().x, chartTop + chartHeight)
        lineTo(points.first().x, chartTop + chartHeight)
        close()
    }
    
    drawPath(
        path = areaPath,
        brush = Brush.verticalGradient(
            colors = listOf(
                Color(0xFF4CAF50).copy(alpha = 0.3f * animatedProgress),
                Color.Transparent
            ),
            startY = chartTop,
            endY = chartTop + chartHeight
        )
    )
    
    // Draw trend line
    drawPath(
        path = trendPath,
        color = Color(0xFF4CAF50),
        style = Stroke(width = 3.dp.toPx(), cap = StrokeCap.Round)
    )
    
    // Draw data points
    points.forEachIndexed { index, point ->
        val metric = data[index]
        val pointColor = getMetricColor(metric.type)
        
        drawCircle(
            color = pointColor,
            radius = 6.dp.toPx() * animatedProgress,
            center = point
        )
        drawCircle(
            color = Color.White,
            radius = 3.dp.toPx() * animatedProgress,
            center = point
        )
    }
    
    // Draw trend line (linear regression)
    if (data.size > 1) {
        val trendLine = calculateTrendLine(data, chartLeft, chartTop, chartWidth, chartHeight, minValue, valueRange)
        drawLine(
            color = Color(0xFFFF9800).copy(alpha = 0.7f),
            start = trendLine.first,
            end = trendLine.second,
            strokeWidth = 2.dp.toPx(),
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f))
        )
    }
}

private fun DrawScope.drawTrendGrid(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxValue: Float,
    minValue: Float
) {
    val gridLines = 5
    
    // Horizontal grid lines
    for (i in 0..gridLines) {
        val y = chartTop + (chartHeight * i / gridLines)
        drawLine(
            color = Color.Gray.copy(alpha = 0.3f),
            start = Offset(chartLeft, y),
            end = Offset(chartLeft + chartWidth, y),
            strokeWidth = 1.dp.toPx()
        )
        
        // Y-axis labels
        val value = maxValue - (maxValue - minValue) * i / gridLines
        drawContext.canvas.nativeCanvas.drawText(
            String.format("%.1f", value),
            chartLeft - 5.dp.toPx(),
            y + 4.dp.toPx(),
            android.graphics.Paint().apply {
                color = android.graphics.Color.GRAY
                textSize = 10.sp.toPx()
                textAlign = android.graphics.Paint.Align.RIGHT
            }
        )
    }
}

private fun calculateTrendLine(
    data: List<PerformanceMetric>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minValue: Float,
    valueRange: Float
): Pair<Offset, Offset> {
    val n = data.size
    val sumX = (0 until n).sum()
    val sumY = data.sumOf { it.value.toDouble() }.toFloat()
    val sumXY = data.mapIndexed { index, metric -> index * metric.value }.sum()
    val sumX2 = (0 until n).sumOf { it * it }
    
    val slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX)
    val intercept = (sumY - slope * sumX) / n
    
    val startY = intercept
    val endY = slope * (n - 1) + intercept
    
    val startYPixel = chartTop + chartHeight - ((startY - minValue) / valueRange) * chartHeight
    val endYPixel = chartTop + chartHeight - ((endY - minValue) / valueRange) * chartHeight
    
    return Pair(
        Offset(chartLeft, startYPixel),
        Offset(chartLeft + chartWidth, endYPixel)
    )
}

@Composable
private fun PerformanceInsightsSection(
    performanceData: List<PerformanceMetric>,
    selectedMetric: PerformanceMetricType
) {
    val filteredData = performanceData.filter { it.type == selectedMetric }
    val insights = generatePerformanceInsights(filteredData, selectedMetric)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Performance Insights",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            insights.forEach { insight ->
                InsightItem(insight = insight)
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun InsightItem(
    insight: PerformanceInsight
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Icon(
            imageVector = getInsightIcon(insight.type),
            contentDescription = insight.type.name,
            tint = getInsightColor(insight.type),
            modifier = Modifier.size(20.dp)
        )
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = insight.title,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
            Text(
                text = insight.description,
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
        
        if (insight.value != null) {
            Text(
                text = insight.value,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold,
                color = getInsightColor(insight.type)
            )
        }
    }
}

@Composable
private fun SeasonalAnalysisSection(
    performanceData: List<PerformanceMetric>
) {
    val seasonalData = analyzeSeasonalPerformance(performanceData)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Seasonal Performance",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                seasonalData.forEach { (season, performance) ->
                    SeasonalPerformanceItem(
                        season = season,
                        performance = performance
                    )
                }
            }
        }
    }
}

@Composable
private fun SeasonalPerformanceItem(
    season: Season,
    performance: Float
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = getSeasonIcon(season),
            contentDescription = season.name,
            tint = getSeasonColor(season),
            modifier = Modifier.size(24.dp)
        )
        
        Text(
            text = season.displayName,
            fontSize = 10.sp,
            color = Color.Gray
        )
        
        Text(
            text = "${String.format("%.1f", performance)}%",
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

@Composable
private fun PredictiveAnalysisSection(
    performanceData: List<PerformanceMetric>,
    selectedMetric: PerformanceMetricType
) {
    val prediction = generatePrediction(performanceData, selectedMetric)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Predictive Analysis",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Next Month Prediction",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = prediction.nextMonthValue,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
                
                Column(
                    horizontalAlignment = Alignment.End
                ) {
                    Text(
                        text = "Confidence",
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                    Text(
                        text = "${prediction.confidence}%",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = getPredictionConfidenceColor(prediction.confidence)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LinearProgressIndicator(
                progress = { prediction.confidence / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = getPredictionConfidenceColor(prediction.confidence),
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
        }
    }
}

// Using data classes from model package - removed duplicate definitions

data class PerformanceInsight(
    val type: InsightType,
    val title: String,
    val description: String,
    val value: String? = null
)

enum class InsightType {
    POSITIVE, NEGATIVE, NEUTRAL, WARNING
}

enum class Season(val displayName: String) {
    SPRING("Spring"),
    SUMMER("Summer"),
    AUTUMN("Autumn"),
    WINTER("Winter")
}

data class PredictionResult(
    val nextMonthValue: String,
    val confidence: Float
)

// Helper functions
private fun getMetricIcon(metric: PerformanceMetricType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (metric) {
        PerformanceMetricType.GENERATION -> Icons.Default.ElectricBolt
        PerformanceMetricType.EFFICIENCY -> Icons.Default.Speed
        PerformanceMetricType.BATTERY_HEALTH -> Icons.Default.Battery6Bar
        PerformanceMetricType.INVERTER_PERFORMANCE -> Icons.Default.Memory
        PerformanceMetricType.WEATHER_IMPACT -> Icons.Default.WbSunny
        PerformanceMetricType.COST_SAVINGS -> Icons.Default.AttachMoney
    }
}

private fun getMetricColor(metric: PerformanceMetricType): Color {
    return when (metric) {
        PerformanceMetricType.GENERATION -> Color(0xFF4CAF50)
        PerformanceMetricType.EFFICIENCY -> Color(0xFF2196F3)
        PerformanceMetricType.BATTERY_HEALTH -> Color(0xFFFF9800)
        PerformanceMetricType.INVERTER_PERFORMANCE -> Color(0xFF9C27B0)
        PerformanceMetricType.WEATHER_IMPACT -> Color(0xFFFFEB3B)
        PerformanceMetricType.COST_SAVINGS -> Color(0xFF00E5FF)
    }
}

private fun getInsightIcon(type: InsightType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (type) {
        InsightType.POSITIVE -> Icons.Default.TrendingUp
        InsightType.NEGATIVE -> Icons.Default.TrendingDown
        InsightType.NEUTRAL -> Icons.Default.Info
        InsightType.WARNING -> Icons.Default.Warning
    }
}

private fun getInsightColor(type: InsightType): Color {
    return when (type) {
        InsightType.POSITIVE -> Color(0xFF4CAF50)
        InsightType.NEGATIVE -> Color(0xFFF44336)
        InsightType.NEUTRAL -> Color(0xFF2196F3)
        InsightType.WARNING -> Color(0xFFFF9800)
    }
}

private fun getSeasonIcon(season: Season): androidx.compose.ui.graphics.vector.ImageVector {
    return when (season) {
        Season.SPRING -> Icons.Default.LocalFlorist
        Season.SUMMER -> Icons.Default.WbSunny
        Season.AUTUMN -> Icons.Default.Eco
        Season.WINTER -> Icons.Default.AcUnit
    }
}

private fun getSeasonColor(season: Season): Color {
    return when (season) {
        Season.SPRING -> Color(0xFF4CAF50)
        Season.SUMMER -> Color(0xFFFFD700)
        Season.AUTUMN -> Color(0xFFFF9800)
        Season.WINTER -> Color(0xFF2196F3)
    }
}

private fun getPredictionConfidenceColor(confidence: Float): Color {
    return when {
        confidence > 80f -> Color(0xFF4CAF50)
        confidence > 60f -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
}

private fun generatePerformanceInsights(
    data: List<PerformanceMetric>,
    metric: PerformanceMetricType
): List<PerformanceInsight> {
    if (data.isEmpty()) return emptyList()
    
    val insights = mutableListOf<PerformanceInsight>()
    val average = data.map { it.value }.average().toFloat()
    val latest = data.lastOrNull()?.value ?: 0f
    
    // Performance comparison
    when {
        latest > average * 1.1f -> {
            insights.add(
                PerformanceInsight(
                    type = InsightType.POSITIVE,
                    title = "Above Average Performance",
                    description = "Current ${metric.displayName.lowercase()} is ${String.format("%.1f", ((latest - average) / average) * 100)}% above average",
                    value = "+${String.format("%.1f", latest - average)}"
                )
            )
        }
        latest < average * 0.9f -> {
            insights.add(
                PerformanceInsight(
                    type = InsightType.NEGATIVE,
                    title = "Below Average Performance",
                    description = "Current ${metric.displayName.lowercase()} is ${String.format("%.1f", ((average - latest) / average) * 100)}% below average",
                    value = "-${String.format("%.1f", average - latest)}"
                )
            )
        }
        else -> {
            insights.add(
                PerformanceInsight(
                    type = InsightType.NEUTRAL,
                    title = "Stable Performance",
                    description = "Current ${metric.displayName.lowercase()} is within normal range",
                    value = "±${String.format("%.1f", abs(latest - average))}"
                )
            )
        }
    }
    
    return insights
}

private fun analyzeSeasonalPerformance(data: List<PerformanceMetric>): Map<Season, Float> {
    // Mock seasonal analysis - in real implementation, this would analyze actual seasonal data
    return mapOf(
        Season.SPRING to 85.5f,
        Season.SUMMER to 92.3f,
        Season.AUTUMN to 78.9f,
        Season.WINTER to 65.2f
    )
}

private fun generatePrediction(
    data: List<PerformanceMetric>,
    metric: PerformanceMetricType
): PredictionResult {
    if (data.isEmpty()) {
        return PredictionResult("N/A", 0f)
    }
    
    val average = data.map { it.value }.average().toFloat()
    val trend = if (data.size > 1) {
        val recent = data.takeLast(data.size / 2).map { it.value }.average().toFloat()
        val older = data.take(data.size / 2).map { it.value }.average().toFloat()
        (recent - older) / older
    } else 0f
    
    val prediction = average * (1 + trend)
    val confidence = when {
        abs(trend) < 0.05f -> 85f + (15f * (1 - abs(trend) / 0.05f))
        abs(trend) < 0.15f -> 70f + (15f * (1 - abs(trend) / 0.15f))
        else -> 50f + (20f * (1 - abs(trend).coerceAtMost(0.3f) / 0.3f))
    }
    
    val unit = when (metric) {
        PerformanceMetricType.GENERATION -> "kWh"
        PerformanceMetricType.EFFICIENCY -> "%"
        PerformanceMetricType.BATTERY_HEALTH -> "%"
        PerformanceMetricType.INVERTER_PERFORMANCE -> "%"
        PerformanceMetricType.WEATHER_IMPACT -> "%"
        PerformanceMetricType.COST_SAVINGS -> "$"
    }
    
    return PredictionResult(
        nextMonthValue = "${String.format("%.1f", prediction)} $unit",
        confidence = confidence.coerceIn(0f, 100f)
    )
}

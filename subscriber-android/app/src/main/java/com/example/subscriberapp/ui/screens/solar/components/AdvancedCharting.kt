package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
import kotlin.math.*

@Composable
fun InteractiveGenerationChart(
    generationData: List<GenerationDataPoint>,
    chartType: ChartType,
    timeRange: TimeRange,
    onDataPointSelected: (GenerationDataPoint?) -> Unit,
    onExportData: () -> Unit,
    modifier: Modifier = Modifier
) {
    var zoomLevel by remember { mutableFloatStateOf(1f) }
    var panOffset by remember { mutableStateOf(Offset.Zero) }
    var selectedDataPoint by remember { mutableStateOf<GenerationDataPoint?>(null) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // Chart Controls
        ChartControlsBar(
            chartType = chartType,
            timeRange = timeRange,
            zoomLevel = zoomLevel,
            onZoomIn = { zoomLevel = (zoomLevel * 1.2f).coerceAtMost(5f) },
            onZoomOut = { zoomLevel = (zoomLevel / 1.2f).coerceAtLeast(0.5f) },
            onResetZoom = { 
                zoomLevel = 1f
                panOffset = Offset.Zero
            },
            onExport = onExportData
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Interactive Chart
        Card(
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp)
                    .padding(16.dp)
            ) {
                InteractiveChart(
                    data = generationData,
                    chartType = chartType,
                    zoomLevel = zoomLevel,
                    panOffset = panOffset,
                    onPanOffsetChange = { panOffset = it },
                    onDataPointSelected = { point ->
                        selectedDataPoint = point
                        onDataPointSelected(point)
                    },
                    modifier = Modifier.fillMaxSize()
                )
                
                // Data Point Tooltip
                selectedDataPoint?.let { point ->
                    DataPointTooltip(
                        dataPoint = point,
                        onDismiss = { 
                            selectedDataPoint = null
                            onDataPointSelected(null)
                        },
                        modifier = Modifier.align(Alignment.TopEnd)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Chart Statistics
        ChartStatistics(
            data = generationData,
            timeRange = timeRange
        )
    }
}

@Composable
private fun ChartControlsBar(
    chartType: ChartType,
    timeRange: TimeRange,
    zoomLevel: Float,
    onZoomIn: () -> Unit,
    onZoomOut: () -> Unit,
    onResetZoom: () -> Unit,
    onExport: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Chart Info
        Column {
            Text(
                text = "${chartType.name} Chart - ${timeRange.name}",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Text(
                text = "Zoom: ${String.format("%.1f", zoomLevel)}x",
                fontSize = 12.sp,
                color = Color.Gray
            )
        }
        
        // Controls
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            IconButton(
                onClick = onZoomOut,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ZoomOut,
                    contentDescription = "Zoom Out",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            IconButton(
                onClick = onZoomIn,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ZoomIn,
                    contentDescription = "Zoom In",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            IconButton(
                onClick = onResetZoom,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CenterFocusStrong,
                    contentDescription = "Reset Zoom",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }
            
            IconButton(
                onClick = onExport,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.FileDownload,
                    contentDescription = "Export Data",
                    tint = Color(0xFFE94560),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
private fun InteractiveChart(
    data: List<GenerationDataPoint>,
    chartType: ChartType,
    zoomLevel: Float,
    panOffset: Offset,
    onPanOffsetChange: (Offset) -> Unit,
    onDataPointSelected: (GenerationDataPoint?) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    
    Canvas(
        modifier = modifier
            .pointerInput(Unit) {
                detectDragGestures { change, dragAmount ->
                    onPanOffsetChange(panOffset + dragAmount)
                }
            }
            .pointerInput(Unit) {
                detectTapGestures { tapOffset ->
                    // Find nearest data point to tap
                    val nearestPoint = findNearestDataPoint(
                        data = data,
                        tapOffset = tapOffset,
                        canvasSize = androidx.compose.ui.geometry.Size(size.width.toFloat(), size.height.toFloat()),
                        zoomLevel = zoomLevel,
                        panOffset = panOffset
                    )
                    onDataPointSelected(nearestPoint)
                }
            }
    ) {
        when (chartType) {
            ChartType.LINE -> drawInteractiveLineChart(
                data = data,
                zoomLevel = zoomLevel,
                panOffset = panOffset
            )
            ChartType.BAR -> drawInteractiveBarChart(
                data = data,
                zoomLevel = zoomLevel,
                panOffset = panOffset
            )
            ChartType.AREA -> drawInteractiveAreaChart(
                data = data,
                zoomLevel = zoomLevel,
                panOffset = panOffset
            )
            else -> drawInteractiveLineChart(data, zoomLevel, panOffset)
        }
    }
}

private fun DrawScope.drawInteractiveLineChart(
    data: List<GenerationDataPoint>,
    zoomLevel: Float,
    panOffset: Offset
) {
    if (data.isEmpty()) return
    
    val chartWidth = size.width * zoomLevel
    val chartHeight = size.height - 60.dp.toPx()
    val chartLeft = 30.dp.toPx() + panOffset.x
    val chartTop = 30.dp.toPx() + panOffset.y
    
    val maxGeneration = data.maxOfOrNull { it.powerGenerated } ?: 1f
    val minGeneration = data.minOfOrNull { it.powerGenerated } ?: 0f
    val generationRange = maxGeneration - minGeneration
    
    val pointSpacing = chartWidth / (data.size - 1).coerceAtLeast(1)
    
    // Draw grid lines
    drawChartGrid(chartLeft, chartTop, chartWidth, chartHeight, maxGeneration, minGeneration)
    
    // Create path for line
    val path = Path()
    val points = mutableListOf<Offset>()
    
    data.forEachIndexed { index, dataPoint ->
        val x = chartLeft + index * pointSpacing
        val y = chartTop + chartHeight - ((dataPoint.powerGenerated - minGeneration) / generationRange) * chartHeight
        
        points.add(Offset(x, y))
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    // Draw gradient fill under line
    val gradientPath = Path().apply {
        addPath(path)
        lineTo(points.last().x, chartTop + chartHeight)
        lineTo(points.first().x, chartTop + chartHeight)
        close()
    }
    
    drawPath(
        path = gradientPath,
        brush = Brush.verticalGradient(
            colors = listOf(
                Color(0xFF4CAF50).copy(alpha = 0.3f),
                Color.Transparent
            ),
            startY = chartTop,
            endY = chartTop + chartHeight
        )
    )
    
    // Draw line
    drawPath(
        path = path,
        color = Color(0xFF4CAF50),
        style = Stroke(width = 3.dp.toPx(), cap = StrokeCap.Round)
    )
    
    // Draw data points
    points.forEachIndexed { index, point ->
        val dataPoint = data[index]
        val pointColor = getDataPointColor(dataPoint.efficiency)
        
        drawCircle(
            color = pointColor,
            radius = 6.dp.toPx(),
            center = point
        )
        drawCircle(
            color = Color.White,
            radius = 3.dp.toPx(),
            center = point
        )
    }
}

private fun DrawScope.drawInteractiveBarChart(
    data: List<GenerationDataPoint>,
    zoomLevel: Float,
    panOffset: Offset
) {
    if (data.isEmpty()) return
    
    val chartWidth = size.width * zoomLevel
    val chartHeight = size.height - 60.dp.toPx()
    val chartLeft = 30.dp.toPx() + panOffset.x
    val chartTop = 30.dp.toPx() + panOffset.y
    
    val maxGeneration = data.maxOfOrNull { it.powerGenerated } ?: 1f
    val barWidth = (chartWidth / data.size) * 0.8f
    val barSpacing = chartWidth / data.size
    
    // Draw grid lines
    drawChartGrid(chartLeft, chartTop, chartWidth, chartHeight, maxGeneration, 0f)
    
    // Draw bars
    data.forEachIndexed { index, dataPoint ->
        val barHeight = (dataPoint.powerGenerated / maxGeneration) * chartHeight
        val barLeft = chartLeft + index * barSpacing + barSpacing * 0.1f
        val barTop = chartTop + chartHeight - barHeight
        
        val barColor = getDataPointColor(dataPoint.efficiency)
        
        // Bar gradient
        val barBrush = Brush.verticalGradient(
            colors = listOf(
                barColor,
                barColor.copy(alpha = 0.7f)
            ),
            startY = barTop,
            endY = barTop + barHeight
        )
        
        drawRect(
            brush = barBrush,
            topLeft = Offset(barLeft, barTop),
            size = Size(barWidth, barHeight)
        )
        
        // Bar outline
        drawRect(
            color = barColor,
            topLeft = Offset(barLeft, barTop),
            size = Size(barWidth, barHeight),
            style = Stroke(width = 1.dp.toPx())
        )
    }
}

private fun DrawScope.drawInteractiveAreaChart(
    data: List<GenerationDataPoint>,
    zoomLevel: Float,
    panOffset: Offset
) {
    if (data.isEmpty()) return
    
    val chartWidth = size.width * zoomLevel
    val chartHeight = size.height - 60.dp.toPx()
    val chartLeft = 30.dp.toPx() + panOffset.x
    val chartTop = 30.dp.toPx() + panOffset.y
    
    val maxGeneration = data.maxOfOrNull { it.powerGenerated } ?: 1f
    val minGeneration = data.minOfOrNull { it.powerGenerated } ?: 0f
    val generationRange = maxGeneration - minGeneration
    
    val pointSpacing = chartWidth / (data.size - 1).coerceAtLeast(1)
    
    // Draw grid lines
    drawChartGrid(chartLeft, chartTop, chartWidth, chartHeight, maxGeneration, minGeneration)
    
    // Create area path
    val areaPath = Path()
    
    data.forEachIndexed { index, dataPoint ->
        val x = chartLeft + index * pointSpacing
        val y = chartTop + chartHeight - ((dataPoint.powerGenerated - minGeneration) / generationRange) * chartHeight
        
        if (index == 0) {
            areaPath.moveTo(x, chartTop + chartHeight)
            areaPath.lineTo(x, y)
        } else {
            areaPath.lineTo(x, y)
        }
    }
    
    // Close the area
    areaPath.lineTo(chartLeft + (data.size - 1) * pointSpacing, chartTop + chartHeight)
    areaPath.close()
    
    // Draw area with gradient
    drawPath(
        path = areaPath,
        brush = Brush.verticalGradient(
            colors = listOf(
                Color(0xFF4CAF50).copy(alpha = 0.6f),
                Color(0xFF4CAF50).copy(alpha = 0.1f)
            ),
            startY = chartTop,
            endY = chartTop + chartHeight
        )
    )
    
    // Draw area outline
    drawPath(
        path = areaPath,
        color = Color(0xFF4CAF50),
        style = Stroke(width = 2.dp.toPx())
    )
}

private fun DrawScope.drawChartGrid(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    maxValue: Float,
    minValue: Float
) {
    val gridLines = 5
    
    // Horizontal grid lines
    for (i in 0..gridLines) {
        val y = chartTop + (chartHeight * i / gridLines)
        drawLine(
            color = Color.Gray.copy(alpha = 0.3f),
            start = Offset(chartLeft, y),
            end = Offset(chartLeft + chartWidth, y),
            strokeWidth = 1.dp.toPx()
        )
        
        // Y-axis labels
        val value = maxValue - (maxValue - minValue) * i / gridLines
        drawContext.canvas.nativeCanvas.drawText(
            String.format("%.1f", value),
            chartLeft - 5.dp.toPx(),
            y + 4.dp.toPx(),
            android.graphics.Paint().apply {
                color = android.graphics.Color.GRAY
                textSize = 10.sp.toPx()
                textAlign = android.graphics.Paint.Align.RIGHT
            }
        )
    }
    
    // Vertical grid lines
    val verticalLines = 6
    for (i in 0..verticalLines) {
        val x = chartLeft + (chartWidth * i / verticalLines)
        drawLine(
            color = Color.Gray.copy(alpha = 0.3f),
            start = Offset(x, chartTop),
            end = Offset(x, chartTop + chartHeight),
            strokeWidth = 1.dp.toPx()
        )
    }
}

private fun getDataPointColor(efficiency: Float): Color {
    return when {
        efficiency > 90f -> Color(0xFF4CAF50)
        efficiency > 80f -> Color(0xFF8BC34A)
        efficiency > 70f -> Color(0xFFFFEB3B)
        efficiency > 60f -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
}

private fun findNearestDataPoint(
    data: List<GenerationDataPoint>,
    tapOffset: Offset,
    canvasSize: androidx.compose.ui.geometry.Size,
    zoomLevel: Float,
    panOffset: Offset
): GenerationDataPoint? {
    if (data.isEmpty()) return null
    
    val chartWidth = canvasSize.width * zoomLevel
    val chartHeight = canvasSize.height - 60
    val chartLeft = 30 + panOffset.x
    val chartTop = 30 + panOffset.y
    
    val pointSpacing = chartWidth / (data.size - 1).coerceAtLeast(1)
    val tapThreshold = 50f // pixels
    
    var nearestPoint: GenerationDataPoint? = null
    var minDistance = Float.MAX_VALUE
    
    data.forEachIndexed { index, dataPoint ->
        val pointX = chartLeft + index * pointSpacing
        val distance = abs(tapOffset.x - pointX)
        
        if (distance < minDistance && distance < tapThreshold) {
            minDistance = distance
            nearestPoint = dataPoint
        }
    }
    
    return nearestPoint
}

@Composable
private fun DataPointTooltip(
    dataPoint: GenerationDataPoint,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.9f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Data Point",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                IconButton(
                    onClick = onDismiss,
                    modifier = Modifier.size(20.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color.Gray,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "Time: ${dataPoint.timestamp}", // timestamp is already a String
                fontSize = 10.sp,
                color = Color.Gray
            )
            
            Text(
                text = "Power: ${String.format("%.2f", dataPoint.powerGenerated)} kW",
                fontSize = 10.sp,
                color = Color.White
            )
            
            Text(
                text = "Efficiency: ${String.format("%.1f", dataPoint.efficiency)}%",
                fontSize = 10.sp,
                color = getDataPointColor(dataPoint.efficiency)
            )
            
            Text(
                text = "Weather: ${dataPoint.weatherCondition.name}",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

@Composable
private fun ChartStatistics(
    data: List<GenerationDataPoint>,
    timeRange: TimeRange
) {
    if (data.isEmpty()) return
    
    val totalGeneration = data.sumOf { it.powerGenerated.toDouble() }.toFloat()
    val averageGeneration = totalGeneration / data.size
    val maxGeneration = data.maxOfOrNull { it.powerGenerated } ?: 0f
    val minGeneration = data.minOfOrNull { it.powerGenerated } ?: 0f
    val averageEfficiency = data.map { it.efficiency }.average().toFloat()
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Chart Statistics",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticItem(
                    label = "Total",
                    value = "${String.format("%.1f", totalGeneration)} kWh",
                    color = Color(0xFF4CAF50)
                )
                
                StatisticItem(
                    label = "Average",
                    value = "${String.format("%.2f", averageGeneration)} kW",
                    color = Color(0xFF2196F3)
                )
                
                StatisticItem(
                    label = "Peak",
                    value = "${String.format("%.2f", maxGeneration)} kW",
                    color = Color(0xFFFF9800)
                )
                
                StatisticItem(
                    label = "Efficiency",
                    value = "${String.format("%.1f", averageEfficiency)}%",
                    color = Color(0xFFE94560)
                )
            }
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
        Text(
            text = value,
            fontSize = 12.sp,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

package com.example.subscriberapp.ui.screens.iot

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.components.NeonCard

data class SmartLight(
    val id: String,
    val name: String,
    val room: String,
    val isOn: <PERSON><PERSON>an,
    val brightness: Float,
    val color: Color,
    val isConnected: Boolean = true
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartLightingScreen(
    onNavigateBack: () -> Unit
) {
    var lights by remember {
        mutableStateOf(
            listOf(
                SmartLight("1", "Ceiling Light", "Living Room", true, 0.8f, Color(0xFFFFD700)),
                SmartLight("2", "Table Lamp", "Living Room", true, 0.6f, Color(0xFFFF6B9D)),
                SmartLight("3", "Bedroom Light", "Bedroom", false, 0.5f, Color(0xFF4ECDC4)),
                SmartLight("4", "Kitchen Light", "Kitchen", true, 0.9f, Color.White),
                SmartLight("5", "Bathroom Light", "Bathroom", true, 0.7f, Color(0xFF667eea)),
                SmartLight("6", "Hallway Light", "Hallway", false, 0.4f, Color(0xFF8BC34A))
            )
        )
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "Smart Lighting",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Quick Controls
            NeonCard(
                modifier = Modifier.fillMaxWidth(),
                glowColor = Color(0xFFFFD700)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    Text(
                        text = "Quick Controls",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        QuickControlButton(
                            icon = Icons.Default.Lightbulb,
                            label = "All On",
                            color = Color(0xFF4CAF50)
                        ) {
                            lights = lights.map { it.copy(isOn = true) }
                        }
                        
                        QuickControlButton(
                            icon = Icons.Default.PowerOff,
                            label = "All Off",
                            color = Color(0xFFF44336)
                        ) {
                            lights = lights.map { it.copy(isOn = false) }
                        }
                        
                        QuickControlButton(
                            icon = Icons.Default.Bedtime,
                            label = "Night Mode",
                            color = Color(0xFF9C27B0)
                        ) {
                            lights = lights.map { 
                                it.copy(
                                    isOn = true, 
                                    brightness = 0.2f,
                                    color = Color(0xFF4A148C)
                                ) 
                            }
                        }
                        
                        QuickControlButton(
                            icon = Icons.Default.WbSunny,
                            label = "Bright",
                            color = Color(0xFFFFD700)
                        ) {
                            lights = lights.map { 
                                it.copy(
                                    isOn = true, 
                                    brightness = 1.0f,
                                    color = Color.White
                                ) 
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Lights List
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(lights) { light ->
                    SmartLightCard(
                        light = light,
                        onToggle = { lightId ->
                            lights = lights.map { 
                                if (it.id == lightId) it.copy(isOn = !it.isOn) else it 
                            }
                        },
                        onBrightnessChange = { lightId, brightness ->
                            lights = lights.map { 
                                if (it.id == lightId) it.copy(brightness = brightness) else it 
                            }
                        },
                        onColorChange = { lightId, color ->
                            lights = lights.map { 
                                if (it.id == lightId) it.copy(color = color) else it 
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun QuickControlButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    color: Color,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        IconButton(
            onClick = onClick,
            modifier = Modifier
                .size(56.dp)
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            color.copy(alpha = 0.3f),
                            color.copy(alpha = 0.1f)
                        )
                    )
                )
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

@Composable
fun SmartLightCard(
    light: SmartLight,
    onToggle: (String) -> Unit,
    onBrightnessChange: (String, Float) -> Unit,
    onColorChange: (String, Color) -> Unit
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth(),
        glowColor = if (light.isOn) light.color else Color.Gray
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = light.name,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        text = light.room,
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
                
                Switch(
                    checked = light.isOn,
                    onCheckedChange = { onToggle(light.id) },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = light.color,
                        checkedTrackColor = light.color.copy(alpha = 0.5f)
                    )
                )
            }
            
            if (light.isOn) {
                Spacer(modifier = Modifier.height(16.dp))
                
                // Brightness Control
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Brightness",
                            fontSize = 14.sp,
                            color = Color.White
                        )
                        Text(
                            text = "${(light.brightness * 100).toInt()}%",
                            fontSize = 14.sp,
                            color = light.color
                        )
                    }
                    
                    Slider(
                        value = light.brightness,
                        onValueChange = { onBrightnessChange(light.id, it) },
                        colors = SliderDefaults.colors(
                            thumbColor = light.color,
                            activeTrackColor = light.color,
                            inactiveTrackColor = Color.Gray
                        )
                    )
                }
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Color Presets
                Text(
                    text = "Colors",
                    fontSize = 14.sp,
                    color = Color.White,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    val colors = listOf(
                        Color.White,
                        Color(0xFFFFD700),
                        Color(0xFFFF6B9D),
                        Color(0xFF4ECDC4),
                        Color(0xFF667eea),
                        Color(0xFF8BC34A)
                    )
                    
                    colors.forEach { color ->
                        Box(
                            modifier = Modifier
                                .size(32.dp)
                                .clip(CircleShape)
                                .background(color)
                                .then(
                                    if (light.color == color) {
                                        Modifier.background(
                                            Color.White.copy(alpha = 0.3f),
                                            CircleShape
                                        )
                                    } else Modifier
                                )
                        ) {
                            IconButton(
                                onClick = { onColorChange(light.id, color) },
                                modifier = Modifier.fillMaxSize()
                            ) {}
                        }
                    }
                }
            }
        }
    }
}

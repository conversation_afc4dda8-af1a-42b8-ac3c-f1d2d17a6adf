package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*

@Composable
fun EnhancedAnalyticsDashboard(
    historicalData: List<HistoricalDataSet>,
    performanceMetrics: List<PerformanceMetric>,
    isLoading: <PERSON>olean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Enhanced Analytics Dashboard",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFFE94560)
                )
            }
        } else {
            // Key Performance Indicators
            KeyPerformanceIndicators(
                historicalData = historicalData,
                performanceMetrics = performanceMetrics
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Performance Overview Cards
            PerformanceOverviewCards(
                performanceMetrics = performanceMetrics
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Trend Analysis Summary
            TrendAnalysisSummary(
                historicalData = historicalData
            )
        }
    }
}

@Composable
private fun KeyPerformanceIndicators(
    historicalData: List<HistoricalDataSet>,
    performanceMetrics: List<PerformanceMetric>
) {
    val totalGeneration = historicalData.sumOf { it.totalGeneration.toDouble() }.toFloat()
    val avgEfficiency = if (historicalData.isNotEmpty()) {
        historicalData.map { it.averageEfficiency }.average().toFloat()
    } else 0f
    
    val peakGeneration = historicalData.maxOfOrNull { it.peakGeneration } ?: 0f
    
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        KPICard(
            title = "Total Generation",
            value = "${String.format("%.1f", totalGeneration)} kWh",
            icon = Icons.Default.ElectricBolt,
            color = Color(0xFF4CAF50),
            modifier = Modifier.weight(1f)
        )
        
        KPICard(
            title = "Avg Efficiency",
            value = "${String.format("%.1f", avgEfficiency)}%",
            icon = Icons.Default.Speed,
            color = Color(0xFF2196F3),
            modifier = Modifier.weight(1f)
        )
        
        KPICard(
            title = "Peak Power",
            value = "${String.format("%.1f", peakGeneration)} kW",
            icon = Icons.Default.TrendingUp,
            color = Color(0xFFFF9800),
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun KPICard(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = value,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Text(
                text = title,
                fontSize = 12.sp,
                color = Color.White.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun PerformanceOverviewCards(
    performanceMetrics: List<PerformanceMetric>
) {
    val metricsByType = performanceMetrics.groupBy { it.type }
    
    LazyColumn(
        modifier = Modifier.height(200.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(metricsByType.entries.toList()) { (type, metrics) ->
            val latestMetric = metrics.maxByOrNull { it.timestamp }
            latestMetric?.let { metric ->
                PerformanceMetricCard(
                    metric = metric
                )
            }
        }
    }
}

@Composable
private fun PerformanceMetricCard(
    metric: PerformanceMetric
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.2f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = getMetricIcon(metric.type),
                contentDescription = null,
                tint = getMetricColor(metric.type),
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = metric.type.displayName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White
                )
                
                Text(
                    text = "${String.format("%.1f", metric.value)} ${metric.unit}",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
            
            Text(
                text = "${metric.confidenceLevel.toInt()}%",
                fontSize = 12.sp,
                color = Color(0xFF00E5FF),
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Composable
private fun TrendAnalysisSummary(
    historicalData: List<HistoricalDataSet>
) {
    if (historicalData.size < 2) return
    
    val sortedData = historicalData.sortedBy { it.date }
    val recentData = sortedData.takeLast(7) // Last 7 days
    val previousData = sortedData.dropLast(7).takeLast(7)
    
    if (recentData.isEmpty() || previousData.isEmpty()) return
    
    val recentAvg = recentData.map { it.totalGeneration }.average().toFloat()
    val previousAvg = previousData.map { it.totalGeneration }.average().toFloat()
    val trend = ((recentAvg - previousAvg) / previousAvg) * 100
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "7-Day Trend Analysis",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = if (trend >= 0) Icons.Default.TrendingUp else Icons.Default.TrendingDown,
                    contentDescription = null,
                    tint = if (trend >= 0) Color(0xFF4CAF50) else Color(0xFFFF5722),
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "${if (trend >= 0) "+" else ""}${String.format("%.1f", trend)}%",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (trend >= 0) Color(0xFF4CAF50) else Color(0xFFFF5722)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "vs previous week",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

private fun getMetricIcon(type: PerformanceMetricType): androidx.compose.ui.graphics.vector.ImageVector {
    return when (type) {
        PerformanceMetricType.GENERATION -> Icons.Default.ElectricBolt
        PerformanceMetricType.EFFICIENCY -> Icons.Default.Speed
        PerformanceMetricType.BATTERY_HEALTH -> Icons.Default.Battery6Bar
        PerformanceMetricType.INVERTER_PERFORMANCE -> Icons.Default.Memory
        PerformanceMetricType.WEATHER_IMPACT -> Icons.Default.WbSunny
        PerformanceMetricType.COST_SAVINGS -> Icons.Default.AttachMoney
    }
}

private fun getMetricColor(type: PerformanceMetricType): Color {
    return when (type) {
        PerformanceMetricType.GENERATION -> Color(0xFF4CAF50)
        PerformanceMetricType.EFFICIENCY -> Color(0xFF2196F3)
        PerformanceMetricType.BATTERY_HEALTH -> Color(0xFFFF9800)
        PerformanceMetricType.INVERTER_PERFORMANCE -> Color(0xFF9C27B0)
        PerformanceMetricType.WEATHER_IMPACT -> Color(0xFFFFEB3B)
        PerformanceMetricType.COST_SAVINGS -> Color(0xFF00E5FF)
    }
}

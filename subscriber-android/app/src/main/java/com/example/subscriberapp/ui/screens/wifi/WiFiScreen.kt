package com.example.subscriberapp.ui.screens.wifi

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Wifi
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.services.CheckInResponse
import com.example.subscriberapp.services.CheckInResult
import com.example.subscriberapp.services.CyclingResultsManager
import com.example.subscriberapp.ui.components.GradientButton
import com.example.subscriberapp.ui.components.NeonCard
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WiFiScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val cyclingManager = remember { CyclingResultsManager(context) }
    
    var wifiState by remember { mutableStateOf(WiFiState.READY) }
    var wifiResult by remember { mutableStateOf<CheckInResponse?>(null) }
    
    LaunchedEffect(wifiState) {
        when (wifiState) {
            WiFiState.CONNECTING -> {
                delay(2500) // Simulate 2.5 seconds of WiFi connection
                val result = cyclingManager.getWiFiResult()
                wifiResult = result
                wifiState = WiFiState.RESULT
            }
            WiFiState.RESULT -> {
                delay(3000) // Show result for 3 seconds
                wifiState = WiFiState.READY
                wifiResult = null
            }
            else -> {}
        }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E),
                        Color(0xFF16213E),
                        Color(0xFF0F3460)
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Top Bar
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onNavigateBack) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back",
                        tint = Color.White
                    )
                }
                
                Text(
                    text = "WiFi Check-in",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // Main Content
            when (wifiState) {
                WiFiState.READY -> {
                    WiFiReadyContent(
                        onStartWiFi = { wifiState = WiFiState.CONNECTING }
                    )
                }
                WiFiState.CONNECTING -> {
                    WiFiConnectingContent()
                }
                WiFiState.RESULT -> {
                    wifiResult?.let { result ->
                        WiFiResultContent(result = result)
                    }
                }
            }
        }
    }
}

enum class WiFiState {
    READY, CONNECTING, RESULT
}

@Composable
fun WiFiReadyContent(onStartWiFi: () -> Unit) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF667eea)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Wifi,
                    contentDescription = "WiFi",
                    tint = Color(0xFF667eea),
                    modifier = Modifier.size(80.dp)
                )
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "WiFi Check-in",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = "Connect to the secure network for automatic check-in",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Mock network info
                NeonCard(
                    modifier = Modifier.fillMaxWidth(),
                    glowColor = Color(0xFF667eea).copy(alpha = 0.5f)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Available Network:",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        Text(
                            text = "AttendanceSystem_Secure",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.White
                        )
                        Text(
                            text = "Signal: Strong • Security: WPA3",
                            fontSize = 12.sp,
                            color = Color(0xFF667eea),
                            modifier = Modifier.padding(top = 4.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                GradientButton(
                    text = "Connect & Check-in",
                    onClick = onStartWiFi,
                    modifier = Modifier.fillMaxWidth(),
                    colors = listOf(Color(0xFF667eea), Color(0xFF764ba2))
                )
            }
        }
    }
}

@Composable
fun WiFiConnectingContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = Color(0xFF667eea)
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // WiFi connecting animation
                Box(
                    modifier = Modifier
                        .size(200.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color(0xFF667eea).copy(alpha = 0.3f),
                                    Color.Transparent
                                )
                            )
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Default.Wifi,
                            contentDescription = "WiFi Connecting",
                            tint = Color(0xFF667eea),
                            modifier = Modifier.size(80.dp)
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        CircularProgressIndicator(
                            color = Color(0xFF667eea),
                            modifier = Modifier.size(32.dp)
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                Text(
                    text = "Connecting to WiFi...",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = "Authenticating with secure network",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Connection steps
                Column {
                    ConnectionStep("Scanning networks...", true)
                    ConnectionStep("Connecting to AttendanceSystem_Secure...", true)
                    ConnectionStep("Authenticating credentials...", false)
                    ConnectionStep("Verifying check-in permissions...", false)
                }
            }
        }
    }
}

@Composable
fun ConnectionStep(text: String, completed: Boolean) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (completed) {
            Text(
                text = "✓",
                color = Color(0xFF4CAF50),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold
            )
        } else {
            CircularProgressIndicator(
                color = Color(0xFF667eea),
                modifier = Modifier.size(16.dp),
                strokeWidth = 2.dp
            )
        }
        
        Spacer(modifier = Modifier.width(12.dp))
        
        Text(
            text = text,
            color = if (completed) Color.White else Color.Gray,
            fontSize = 14.sp
        )
    }
}

@Composable
fun WiFiResultContent(result: CheckInResponse) {
    val resultColor = when (result.result) {
        CheckInResult.SUCCESS -> Color(0xFF4CAF50)
        CheckInResult.EXPIRED -> Color(0xFFFF9800)
        CheckInResult.INVALID -> Color(0xFFF44336)
    }
    
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        NeonCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp),
            glowColor = resultColor
        ) {
            Column(
                modifier = Modifier.padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = result.icon,
                    fontSize = 64.sp,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                Text(
                    text = result.title,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = result.message,
                    fontSize = 16.sp,
                    color = Color.Gray,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
    }
}

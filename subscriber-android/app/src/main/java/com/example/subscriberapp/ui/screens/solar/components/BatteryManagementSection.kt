package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.BatteryStatus
import kotlin.math.*

@Composable
fun BatteryManagementSection(
    batteryStatus: BatteryStatus,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "Battery Management",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFFE94560))
            }
        } else {
            // Main Battery Visualization
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // Animated Battery Icon
                AnimatedBatteryIcon(
                    chargePercentage = batteryStatus.chargePercentage,
                    isCharging = batteryStatus.isCharging,
                    modifier = Modifier.weight(1f)
                )
                
                // Battery Stats
                BatteryStatsColumn(
                    batteryStatus = batteryStatus,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Battery Health and Temperature
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                BatteryHealthCard(
                    healthPercentage = batteryStatus.healthPercentage,
                    modifier = Modifier.weight(1f)
                )
                
                BatteryTemperatureCard(
                    temperature = batteryStatus.temperature,
                    modifier = Modifier.weight(1f)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Charge/Discharge Rate and Time Estimates
            BatteryRateAndTimeInfo(batteryStatus = batteryStatus)
        }
    }
}

@Composable
private fun AnimatedBatteryIcon(
    chargePercentage: Float,
    isCharging: Boolean,
    modifier: Modifier = Modifier
) {
    // Charging animation
    val infiniteTransition = rememberInfiniteTransition(label = "battery_animation")
    
    val chargingAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "charging_alpha"
    )
    
    val fillAnimation by animateFloatAsState(
        targetValue = chargePercentage / 100f,
        animationSpec = tween(1000, easing = FastOutSlowInEasing),
        label = "fill_animation"
    )
    
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.padding(16.dp)
    ) {
        Canvas(
            modifier = Modifier.size(120.dp)
        ) {
            drawBatteryIcon(
                fillLevel = fillAnimation,
                isCharging = isCharging,
                chargingAlpha = if (isCharging) chargingAlpha else 1f
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "${chargePercentage.toInt()}%",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = getBatteryColor(chargePercentage)
        )
        
        Text(
            text = if (isCharging) "Charging" else "Discharging",
            fontSize = 12.sp,
            color = Color.Gray
        )
    }
}

private fun DrawScope.drawBatteryIcon(
    fillLevel: Float,
    isCharging: Boolean,
    chargingAlpha: Float
) {
    val batteryWidth = size.width * 0.6f
    val batteryHeight = size.height * 0.8f
    val batteryX = (size.width - batteryWidth) / 2
    val batteryY = (size.height - batteryHeight) / 2
    
    val terminalWidth = batteryWidth * 0.3f
    val terminalHeight = batteryHeight * 0.1f
    val terminalX = batteryX + (batteryWidth - terminalWidth) / 2
    val terminalY = batteryY - terminalHeight
    
    // Battery outline
    drawRoundRect(
        color = Color.White,
        topLeft = Offset(batteryX, batteryY),
        size = Size(batteryWidth, batteryHeight),
        cornerRadius = CornerRadius(8.dp.toPx()),
        style = Stroke(width = 3.dp.toPx())
    )
    
    // Battery terminal
    drawRoundRect(
        color = Color.White,
        topLeft = Offset(terminalX, terminalY),
        size = Size(terminalWidth, terminalHeight),
        cornerRadius = CornerRadius(4.dp.toPx()),
        style = Stroke(width = 2.dp.toPx())
    )
    
    // Battery fill
    val fillHeight = batteryHeight * fillLevel
    val fillY = batteryY + batteryHeight - fillHeight
    val fillColor = getBatteryFillColor(fillLevel * 100f)
    
    drawRoundRect(
        color = fillColor.copy(alpha = chargingAlpha),
        topLeft = Offset(batteryX + 3.dp.toPx(), fillY),
        size = Size(batteryWidth - 6.dp.toPx(), fillHeight - 3.dp.toPx()),
        cornerRadius = CornerRadius(6.dp.toPx())
    )
    
    // Charging bolt icon
    if (isCharging) {
        val boltPath = Path().apply {
            val centerX = size.width / 2
            val centerY = size.height / 2
            val boltSize = 20.dp.toPx()
            
            moveTo(centerX - boltSize/3, centerY - boltSize/2)
            lineTo(centerX + boltSize/6, centerY - boltSize/2)
            lineTo(centerX - boltSize/6, centerY)
            lineTo(centerX + boltSize/3, centerY)
            lineTo(centerX - boltSize/6, centerY + boltSize/2)
            lineTo(centerX + boltSize/6, centerY + boltSize/2)
            lineTo(centerX + boltSize/6, centerY)
            lineTo(centerX - boltSize/3, centerY)
            close()
        }
        
        drawPath(
            path = boltPath,
            color = Color.Yellow.copy(alpha = chargingAlpha)
        )
    }
}

private fun getBatteryColor(percentage: Float): Color {
    return when {
        percentage > 80f -> Color(0xFF4CAF50)
        percentage > 50f -> Color(0xFFFF9800)
        percentage > 20f -> Color(0xFFFF5722)
        else -> Color(0xFFF44336)
    }
}

private fun getBatteryFillColor(percentage: Float): Color {
    return when {
        percentage > 80f -> Color(0xFF4CAF50)
        percentage > 60f -> Color(0xFF8BC34A)
        percentage > 40f -> Color(0xFFFFEB3B)
        percentage > 20f -> Color(0xFFFF9800)
        else -> Color(0xFFF44336)
    }
}

@Composable
private fun BatteryStatsColumn(
    batteryStatus: BatteryStatus,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        BatteryStatItem(
            icon = Icons.Default.Battery6Bar,
            label = "Capacity",
            value = "${String.format("%.1f", batteryStatus.remainingCapacity)} / ${batteryStatus.totalCapacity} kWh"
        )
        
        BatteryStatItem(
            icon = if (batteryStatus.isCharging) Icons.Default.BatteryChargingFull else Icons.Default.BatteryAlert,
            label = if (batteryStatus.isCharging) "Charge Rate" else "Discharge Rate",
            value = "${String.format("%.1f", if (batteryStatus.isCharging) batteryStatus.chargeRate else batteryStatus.dischargeRate)} kW"
        )
        
        BatteryStatItem(
            icon = Icons.Default.Refresh,
            label = "Cycles",
            value = "${batteryStatus.cycleCount}"
        )
    }
}

@Composable
private fun BatteryStatItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    value: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = Color(0xFFE94560),
            modifier = Modifier.size(20.dp)
        )
        
        Column {
            Text(
                text = label,
                fontSize = 12.sp,
                color = Color.Gray
            )
            Text(
                text = value,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
        }
    }
}

@Composable
private fun BatteryHealthCard(
    healthPercentage: Float,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Favorite,
                contentDescription = "Battery Health",
                tint = getHealthColor(healthPercentage),
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Health",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = "${healthPercentage.toInt()}%",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = getHealthColor(healthPercentage)
            )
            
            // Health status indicator
            LinearProgressIndicator(
                progress = { healthPercentage / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp)),
                color = getHealthColor(healthPercentage),
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
        }
    }
}

@Composable
private fun BatteryTemperatureCard(
    temperature: Float,
    modifier: Modifier = Modifier
) {
    val temperatureColor = getTemperatureColor(temperature)
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Thermostat,
                contentDescription = "Temperature",
                tint = temperatureColor,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Temperature",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = "${temperature.toInt()}°C",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = temperatureColor
            )
            
            Text(
                text = getTemperatureStatus(temperature),
                fontSize = 10.sp,
                color = temperatureColor
            )
        }
    }
}

@Composable
private fun BatteryRateAndTimeInfo(
    batteryStatus: BatteryStatus
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Time to full/empty
        Card(
            modifier = Modifier.weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "Time Estimate",
                    tint = Color(0xFF2196F3),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = if (batteryStatus.isCharging) "Time to Full" else "Time to Empty",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                val timeEstimate = if (batteryStatus.isCharging) {
                    batteryStatus.estimatedTimeToFull
                } else {
                    batteryStatus.estimatedTimeToEmpty
                }
                
                Text(
                    text = timeEstimate ?: "N/A", // timeEstimate is already a String
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        }
        
        // Last maintenance
        Card(
            modifier = Modifier.weight(1f),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.Build,
                    contentDescription = "Last Maintenance",
                    tint = Color(0xFFFF9800),
                    modifier = Modifier.size(24.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Last Maintenance",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Text(
                    text = batteryStatus.lastMaintenanceDate?.toString() ?: "Never",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        }
    }
}

private fun getHealthColor(healthPercentage: Float): Color {
    return when {
        healthPercentage > 90f -> Color(0xFF4CAF50)
        healthPercentage > 70f -> Color(0xFFFF9800)
        healthPercentage > 50f -> Color(0xFFFF5722)
        else -> Color(0xFFF44336)
    }
}

private fun getTemperatureColor(temperature: Float): Color {
    return when {
        temperature < 10f -> Color(0xFF2196F3)
        temperature < 25f -> Color(0xFF4CAF50)
        temperature < 35f -> Color(0xFFFF9800)
        temperature < 45f -> Color(0xFFFF5722)
        else -> Color(0xFFF44336)
    }
}

private fun getTemperatureStatus(temperature: Float): String {
    return when {
        temperature < 10f -> "Cold"
        temperature < 25f -> "Optimal"
        temperature < 35f -> "Warm"
        temperature < 45f -> "Hot"
        else -> "Critical"
    }
}

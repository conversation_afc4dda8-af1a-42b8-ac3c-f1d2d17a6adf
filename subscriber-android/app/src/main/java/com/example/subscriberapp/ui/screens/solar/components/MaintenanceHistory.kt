package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*
// Removed kotlinx.datetime import - using String for dates

@Composable
fun MaintenanceHistory(
    maintenanceHistory: List<MaintenanceRecord>,
    selectedTimeRange: TimeRange,
    onTimeRangeChanged: (TimeRange) -> Unit,
    onRecordSelected: (MaintenanceRecord) -> Unit,
    onGenerateReport: (TimeRange) -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedRecord by remember { mutableStateOf<MaintenanceRecord?>(null) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // History Header with Time Range Selector
        HistoryHeader(
            selectedTimeRange = selectedTimeRange,
            onTimeRangeChanged = onTimeRangeChanged,
            onGenerateReport = onGenerateReport
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Maintenance Statistics
        MaintenanceStatistics(
            history = maintenanceHistory,
            timeRange = selectedTimeRange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Cost Analysis Chart
        CostAnalysisChart(
            history = maintenanceHistory,
            timeRange = selectedTimeRange
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Maintenance Timeline
        MaintenanceTimeline(
            history = maintenanceHistory,
            onRecordSelected = { record ->
                selectedRecord = record
                onRecordSelected(record)
            }
        )
        
        // Selected Record Details
        selectedRecord?.let { record ->
            Spacer(modifier = Modifier.height(16.dp))
            MaintenanceRecordDetails(
                record = record,
                onDismiss = { selectedRecord = null }
            )
        }
    }
}

@Composable
private fun HistoryHeader(
    selectedTimeRange: TimeRange,
    onTimeRangeChanged: (TimeRange) -> Unit,
    onGenerateReport: (TimeRange) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "Maintenance History",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        
        Button(
            onClick = { onGenerateReport(selectedTimeRange) },
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFE94560)
            )
        ) {
            Icon(
                imageVector = Icons.Default.Assessment,
                contentDescription = "Generate Report",
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text("Report", fontSize = 12.sp)
        }
    }
    
    Spacer(modifier = Modifier.height(8.dp))
    
    // Time Range Selector
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(listOf(
            TimeRange.MONTH to "Last Month",
            TimeRange.QUARTER to "Last Quarter", 
            TimeRange.YEAR to "Last Year",
            TimeRange.ALL_TIME to "All Time"
        )) { (range, label) ->
            FilterChip(
                onClick = { onTimeRangeChanged(range) },
                label = { Text(label) },
                selected = selectedTimeRange == range,
                colors = FilterChipDefaults.filterChipColors(
                    selectedContainerColor = Color(0xFFE94560),
                    selectedLabelColor = Color.White,
                    containerColor = Color.Black.copy(alpha = 0.3f),
                    labelColor = Color.Gray
                )
            )
        }
    }
}

@Composable
private fun MaintenanceStatistics(
    history: List<MaintenanceRecord>,
    timeRange: TimeRange
) {
    val totalCost = history.sumOf { it.cost.toDouble() }.toFloat()
    val totalTasks = history.size
    val averageCost = if (totalTasks > 0) totalCost / totalTasks else 0f
    val completedOnTime = history.count { it.completedOnTime }
    val onTimePercentage = if (totalTasks > 0) (completedOnTime.toFloat() / totalTasks) * 100 else 0f
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Maintenance Statistics",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatisticCard(
                    title = "Total Cost",
                    value = "$${String.format("%.0f", totalCost)}",
                    icon = Icons.Default.AttachMoney,
                    color = Color(0xFFE94560)
                )
                
                StatisticCard(
                    title = "Total Tasks",
                    value = totalTasks.toString(),
                    icon = Icons.Default.Assignment,
                    color = Color(0xFF2196F3)
                )
                
                StatisticCard(
                    title = "Avg Cost",
                    value = "$${String.format("%.0f", averageCost)}",
                    icon = Icons.Default.TrendingUp,
                    color = Color(0xFFFF9800)
                )
                
                StatisticCard(
                    title = "On Time",
                    value = "${String.format("%.0f", onTimePercentage)}%",
                    icon = Icons.Default.Schedule,
                    color = Color(0xFF4CAF50)
                )
            }
        }
    }
}

@Composable
private fun StatisticCard(
    title: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = color,
            modifier = Modifier.size(24.dp)
        )
        
        Text(
            text = title,
            fontSize = 10.sp,
            color = Color.Gray,
            modifier = Modifier.padding(top = 4.dp)
        )
        
        Text(
            text = value,
            fontSize = 14.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
    }
}

@Composable
private fun CostAnalysisChart(
    history: List<MaintenanceRecord>,
    timeRange: TimeRange
) {
    val monthlyData = groupMaintenanceByMonth(history)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Cost Analysis",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            if (monthlyData.isNotEmpty()) {
                Canvas(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(150.dp)
                ) {
                    drawCostChart(monthlyData)
                }
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(150.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No maintenance data available",
                        color = Color.Gray
                    )
                }
            }
        }
    }
}

private fun DrawScope.drawCostChart(monthlyData: List<Pair<String, Float>>) {
    val chartWidth = size.width - 60.dp.toPx()
    val chartHeight = size.height - 40.dp.toPx()
    val chartLeft = 30.dp.toPx()
    val chartTop = 20.dp.toPx()
    
    val maxCost = monthlyData.maxOfOrNull { it.second } ?: 1f
    val barWidth = chartWidth / monthlyData.size
    
    monthlyData.forEachIndexed { index, (month, cost) ->
        val barHeight = (cost / maxCost) * chartHeight
        val barLeft = chartLeft + index * barWidth + barWidth * 0.1f
        val barTop = chartTop + chartHeight - barHeight
        val barRight = barLeft + barWidth * 0.8f
        
        // Draw bar
        drawRect(
            color = Color(0xFFE94560),
            topLeft = Offset(barLeft, barTop),
            size = Size(barRight - barLeft, barHeight)
        )
        
        // Draw month label
        drawContext.canvas.nativeCanvas.drawText(
            month,
            barLeft + (barRight - barLeft) / 2,
            chartTop + chartHeight + 15.dp.toPx(),
            android.graphics.Paint().apply {
                color = android.graphics.Color.GRAY
                textSize = 10.sp.toPx()
                textAlign = android.graphics.Paint.Align.CENTER
            }
        )
    }
}

@Composable
private fun MaintenanceTimeline(
    history: List<MaintenanceRecord>,
    onRecordSelected: (MaintenanceRecord) -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Maintenance Timeline",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyColumn(
                modifier = Modifier.height(300.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(history.sortedByDescending { it.completedDate }) { record ->
                    TimelineItem(
                        record = record,
                        onClick = { onRecordSelected(record) }
                    )
                }
            }
        }
    }
}

@Composable
private fun TimelineItem(
    record: MaintenanceRecord,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .background(
                Color.White.copy(alpha = 0.05f),
                RoundedCornerShape(8.dp)
            )
            .padding(12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Timeline indicator
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(12.dp)
                    .background(
                        color = if (record.completedOnTime) Color(0xFF4CAF50) else Color(0xFFFF5722),
                        shape = CircleShape
                    )
            )
            
            Box(
                modifier = Modifier
                    .width(2.dp)
                    .height(20.dp)
                    .background(Color.Gray.copy(alpha = 0.3f))
            )
        }
        
        Spacer(modifier = Modifier.width(16.dp))
        
        // Record details
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = record.taskTitle,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White
            )
            
            Text(
                text = "Completed: ${record.completedDate}",
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = "Cost: $${String.format("%.0f", record.cost)} • ${record.technician}",
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
        
        // Status indicator
        Icon(
            imageVector = if (record.completedOnTime) Icons.Default.CheckCircle else Icons.Default.Warning,
            contentDescription = if (record.completedOnTime) "On time" else "Delayed",
            tint = if (record.completedOnTime) Color(0xFF4CAF50) else Color(0xFFFF5722),
            modifier = Modifier.size(20.dp)
        )
    }
}

@Composable
private fun MaintenanceRecordDetails(
    record: MaintenanceRecord,
    onDismiss: () -> Unit
) {
    Card(
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Maintenance Details",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            DetailRow(label = "Task", value = record.taskTitle)
            DetailRow(label = "Technician", value = record.technician)
            DetailRow(label = "Scheduled Date", value = record.scheduledDate.toString())
            DetailRow(label = "Completed Date", value = record.completedDate.toString())
            DetailRow(label = "Duration", value = record.actualDuration)
            DetailRow(label = "Cost", value = "$${String.format("%.2f", record.cost)}")
            DetailRow(label = "Status", value = if (record.completedOnTime) "On Time" else "Delayed")
            
            if (record.notes.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Notes:",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.White
                )
                Text(
                    text = record.notes,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            if (record.beforeAfterPhotos.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Photo,
                        contentDescription = "Photos",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "${record.beforeAfterPhotos.size} photos attached",
                        fontSize = 12.sp,
                        color = Color(0xFF4CAF50)
                    )
                }
            }
        }
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 12.sp,
            color = Color.Gray
        )
        
        Text(
            text = value,
            fontSize = 12.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium
        )
    }
}

// Data classes
data class MaintenanceRecord(
    val id: String,
    val taskTitle: String,
    val technician: String,
    val scheduledDate: String, // Changed from LocalDate to String
    val completedDate: String, // Changed from LocalDate to String
    val actualDuration: String, // Changed from Duration to String
    val cost: Float,
    val completedOnTime: Boolean,
    val notes: String,
    val beforeAfterPhotos: List<String> = emptyList()
)

// Helper functions
private fun groupMaintenanceByMonth(history: List<MaintenanceRecord>): List<Pair<String, Float>> {
    return history
        .groupBy {
            // Extract month and year from date string (assuming format YYYY-MM-DD)
            val dateParts = it.completedDate.split("-")
            if (dateParts.size >= 2) {
                val monthNames = listOf("Jan", "Feb", "Mar", "Apr", "May", "Jun",
                                      "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")
                val monthIndex = dateParts[1].toIntOrNull()?.minus(1) ?: 0
                val monthName = if (monthIndex in 0..11) monthNames[monthIndex] else "Unknown"
                "$monthName ${dateParts[0]}"
            } else {
                "Unknown"
            }
        }
        .map { (month, records) -> month to records.sumOf { it.cost.toDouble() }.toFloat() }
        .sortedBy { it.first }
}

package com.example.subscriberapp.services

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

data class LoginCredentials(
    val username: String,
    val password: String,
    val description: String
)

class MockAuthService : ViewModel() {
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    private val _currentUser = MutableStateFlow<String?>(null)
    val currentUser: StateFlow<String?> = _currentUser.asStateFlow()
    
    // Hardcoded demo credentials
    val demoCredentials = listOf(
        LoginCredentials("admin", "password123", "Administrator Account"),
        LoginCredentials("user", "demo123", "Regular User Account"),
        LoginCredentials("test", "test123", "Test Account"),
        LoginCredentials("guest", "guest123", "Guest Account")
    )
    
    fun login(username: String, password: String): Boolean {
        val validCredential = demoCredentials.find { 
            it.username == username && it.password == password 
        }
        
        return if (validCredential != null) {
            _isLoggedIn.value = true
            _currentUser.value = validCredential.username
            true
        } else {
            false
        }
    }
    
    fun logout() {
        _isLoggedIn.value = false
        _currentUser.value = null
    }
    
    fun getCurrentUserDisplayName(): String {
        return when (_currentUser.value) {
            "admin" -> "Administrator"
            "user" -> "Regular User"
            "test" -> "Test User"
            "guest" -> "Guest User"
            else -> "Unknown User"
        }
    }
}

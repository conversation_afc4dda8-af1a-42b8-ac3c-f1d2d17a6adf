package com.example.subscriberapp.ui.screens.solar.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.subscriberapp.ui.screens.solar.model.*

@Composable
fun SystemHealthMonitoring(
    healthStatus: SystemHealthStatus,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "System Health",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(color = Color(0xFFE94560))
            }
        } else {
            // Overall Health Score
            OverallHealthCard(
                overallHealth = healthStatus.overallHealth,
                lastCheck = healthStatus.lastHealthCheck
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Component Status Grid
            ComponentStatusGrid(
                inverterStatus = healthStatus.inverterStatus,
                wiringStatus = healthStatus.wiringStatus,
                meterStatus = healthStatus.meterStatus
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Solar Panels Grid
            SolarPanelsGrid(
                panels = healthStatus.solarPanels.take(12) // Show first 12 panels
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Active Alerts
            if (healthStatus.alerts.isNotEmpty()) {
                ActiveAlertsSection(alerts = healthStatus.alerts)
            }
        }
    }
}

@Composable
private fun OverallHealthCard(
    overallHealth: Float,
    lastCheck: String? // Changed from kotlinx.datetime.LocalDateTime to String
) {
    val healthColor = getHealthStatusColor(overallHealth)
    
    // Animated health score
    val animatedHealth by animateFloatAsState(
        targetValue = overallHealth,
        animationSpec = tween(1500, easing = FastOutSlowInEasing),
        label = "health_animation"
    )
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            modifier = Modifier.padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Health Score Circle
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.size(80.dp)
            ) {
                CircularProgressIndicator(
                    progress = { animatedHealth / 100f },
                    modifier = Modifier.fillMaxSize(),
                    color = healthColor,
                    strokeWidth = 6.dp,
                    trackColor = Color.Gray.copy(alpha = 0.3f)
                )
                
                Text(
                    text = "${animatedHealth.toInt()}%",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = healthColor
                )
            }
            
            Spacer(modifier = Modifier.width(20.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "Overall System Health",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = getHealthStatusText(overallHealth),
                    fontSize = 14.sp,
                    color = healthColor
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Last Check: ${lastCheck?.date?.toString() ?: "Never"}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
            
            Icon(
                imageVector = getHealthStatusIcon(overallHealth),
                contentDescription = "Health Status",
                tint = healthColor,
                modifier = Modifier.size(32.dp)
            )
        }
    }
}

@Composable
private fun ComponentStatusGrid(
    inverterStatus: InverterStatus,
    wiringStatus: WiringStatus,
    meterStatus: MeterStatus
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        ComponentStatusCard(
            title = "Inverter",
            status = inverterStatus.status,
            health = inverterStatus.health,
            details = "Efficiency: ${inverterStatus.efficiency.toInt()}%",
            icon = Icons.Default.ElectricBolt,
            modifier = Modifier.weight(1f)
        )
        
        ComponentStatusCard(
            title = "Wiring",
            status = wiringStatus.dcWiring, // Using DC wiring as primary status
            health = wiringStatus.overallHealth,
            details = "Integrity: ${wiringStatus.connectionIntegrity.toInt()}%",
            icon = Icons.Default.Cable,
            modifier = Modifier.weight(1f)
        )
        
        ComponentStatusCard(
            title = "Meter",
            status = if (meterStatus.isConnected) ComponentStatus.NORMAL else ComponentStatus.OFFLINE,
            health = meterStatus.dataAccuracy,
            details = if (meterStatus.isConnected) "Connected" else "Offline",
            icon = Icons.Default.Speed,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun ComponentStatusCard(
    title: String,
    status: ComponentStatus,
    health: Float,
    details: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    val statusColor = getComponentStatusColor(status)
    
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.3f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = statusColor,
                modifier = Modifier.size(28.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = title,
                fontSize = 12.sp,
                color = Color.Gray
            )
            
            Text(
                text = status.name,
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = statusColor
            )
            
            Text(
                text = details,
                fontSize = 10.sp,
                color = Color.Gray
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            LinearProgressIndicator(
                progress = { health / 100f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(3.dp)
                    .clip(RoundedCornerShape(1.5.dp)),
                color = statusColor,
                trackColor = Color.Gray.copy(alpha = 0.3f)
            )
        }
    }
}

@Composable
private fun SolarPanelsGrid(
    panels: List<PanelStatus>
) {
    Column {
        Text(
            text = "Solar Panels Status",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyVerticalGrid(
            columns = GridCells.Fixed(6),
            horizontalArrangement = Arrangement.spacedBy(4.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            modifier = Modifier.height(120.dp)
        ) {
            items(panels) { panel ->
                PanelStatusIndicator(panel = panel)
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // Panel status legend
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            LegendItem(color = Color(0xFF4CAF50), label = "Normal")
            LegendItem(color = Color(0xFFFF9800), label = "Warning")
            LegendItem(color = Color(0xFFF44336), label = "Critical")
            LegendItem(color = Color.Gray, label = "Offline")
        }
    }
}

@Composable
private fun PanelStatusIndicator(
    panel: PanelStatus
) {
    val statusColor = getComponentStatusColor(panel.status)
    val pulseAnimation = rememberInfiniteTransition(label = "panel_pulse")
    
    val alpha by pulseAnimation.animateFloat(
        initialValue = 0.7f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "panel_alpha"
    )
    
    Box(
        modifier = Modifier
            .size(16.dp)
            .background(
                color = statusColor.copy(alpha = if (panel.status != ComponentStatus.NORMAL) alpha else 1f),
                shape = RoundedCornerShape(2.dp)
            )
            .border(
                width = 1.dp,
                color = Color.White.copy(alpha = 0.3f),
                shape = RoundedCornerShape(2.dp)
            )
            .clickable {
                // TODO: Show panel details
            }
    )
}

@Composable
private fun LegendItem(
    color: Color,
    label: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(color = color, shape = CircleShape)
        )
        
        Text(
            text = label,
            fontSize = 10.sp,
            color = Color.Gray
        )
    }
}

@Composable
private fun ActiveAlertsSection(
    alerts: List<SystemAlert>
) {
    Column {
        Text(
            text = "Active Alerts (${alerts.size})",
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyColumn(
            modifier = Modifier.height(150.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(alerts) { alert ->
                AlertCard(alert = alert)
            }
        }
    }
}

@Composable
private fun AlertCard(
    alert: SystemAlert
) {
    val severityColor = getAlertSeverityColor(alert.severity)
    
    Card(
        colors = CardDefaults.cardColors(
            containerColor = severityColor.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(8.dp),
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = getAlertSeverityIcon(alert.severity),
                contentDescription = "Alert",
                tint = severityColor,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = alert.title,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = alert.description,
                    fontSize = 10.sp,
                    color = Color.Gray
                )
                
                if (alert.actionRequired != null) {
                    Text(
                        text = "Action: ${alert.actionRequired}",
                        fontSize = 10.sp,
                        color = severityColor
                    )
                }
            }
            
            Text(
                text = alert.component,
                fontSize = 10.sp,
                color = Color.Gray
            )
        }
    }
}

// Helper functions
private fun getHealthStatusColor(health: Float): Color {
    return when {
        health > 90f -> Color(0xFF4CAF50)
        health > 70f -> Color(0xFFFF9800)
        health > 50f -> Color(0xFFFF5722)
        else -> Color(0xFFF44336)
    }
}

private fun getHealthStatusText(health: Float): String {
    return when {
        health > 90f -> "Excellent"
        health > 70f -> "Good"
        health > 50f -> "Fair"
        else -> "Poor"
    }
}

private fun getHealthStatusIcon(health: Float): androidx.compose.ui.graphics.vector.ImageVector {
    return when {
        health > 90f -> Icons.Default.CheckCircle
        health > 70f -> Icons.Default.Warning
        health > 50f -> Icons.Default.Error
        else -> Icons.Default.Cancel
    }
}

private fun getComponentStatusColor(status: ComponentStatus): Color {
    return when (status) {
        ComponentStatus.NORMAL -> Color(0xFF4CAF50)
        ComponentStatus.WARNING -> Color(0xFFFF9800)
        ComponentStatus.CRITICAL -> Color(0xFFF44336)
        ComponentStatus.OFFLINE -> Color.Gray
    }
}

private fun getAlertSeverityColor(severity: AlertSeverity): Color {
    return when (severity) {
        AlertSeverity.INFO -> Color(0xFF2196F3)
        AlertSeverity.WARNING -> Color(0xFFFF9800)
        AlertSeverity.CRITICAL -> Color(0xFFF44336)
    }
}

private fun getAlertSeverityIcon(severity: AlertSeverity): androidx.compose.ui.graphics.vector.ImageVector {
    return when (severity) {
        AlertSeverity.INFO -> Icons.Default.Info
        AlertSeverity.WARNING -> Icons.Default.Warning
        AlertSeverity.CRITICAL -> Icons.Default.Error
    }
}

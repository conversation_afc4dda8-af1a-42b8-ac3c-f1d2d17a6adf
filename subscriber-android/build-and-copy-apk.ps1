# PowerShell script to build and copy APK
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Building Frontend-Only Subscriber App" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Set-Location $PSScriptRoot

Write-Host "Step 1: Cleaning previous builds..." -ForegroundColor Yellow
& .\gradlew clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "Clean failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 2: Building debug APK..." -ForegroundColor Yellow
& .\gradlew assembleDebug
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Step 3: Copying APK to Desktop..." -ForegroundColor Yellow

# Create destination folder on Desktop
$Desktop = [Environment]::GetFolderPath("Desktop")
$ApkFolder = Join-Path $Desktop "SubscriberApp_APK"
if (!(Test-Path $ApkFolder)) {
    New-Item -ItemType Directory -Path $ApkFolder | Out-Null
}

# Copy the APK file
$SourceApk = "app\build\outputs\apk\debug\app-debug.apk"
$DestApk = Join-Path $ApkFolder "SubscriberApp-Frontend-Only.apk"

if (Test-Path $SourceApk) {
    Copy-Item $SourceApk $DestApk -Force
    
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "✅ BUILD SUCCESSFUL!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "APK Location: $DestApk" -ForegroundColor Cyan
    
    $ApkSize = (Get-Item $DestApk).Length
    Write-Host "APK Size: $([math]::Round($ApkSize / 1MB, 2)) MB" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "The APK has been saved to your Desktop in the folder:" -ForegroundColor White
    Write-Host $ApkFolder -ForegroundColor Yellow
    Write-Host ""
    Write-Host "You can now:" -ForegroundColor White
    Write-Host "1. Install it on your Android device" -ForegroundColor Gray
    Write-Host "2. Share it with others" -ForegroundColor Gray
    Write-Host "3. Test all the frontend features" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Demo Login Credentials:" -ForegroundColor Magenta
    Write-Host "- admin / password123" -ForegroundColor Gray
    Write-Host "- user / demo123" -ForegroundColor Gray
    Write-Host "- test / test123" -ForegroundColor Gray
    Write-Host "- guest / guest123" -ForegroundColor Gray
    Write-Host ""
    
    # Open the folder containing the APK
    Invoke-Item $ApkFolder
    
} else {
    Write-Host "❌ APK file not found at: $SourceApk" -ForegroundColor Red
    Write-Host "Build may have failed or APK location changed." -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"

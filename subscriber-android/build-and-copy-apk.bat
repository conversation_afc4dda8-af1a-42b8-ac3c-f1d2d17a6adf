@echo off
echo ========================================
echo Building Frontend-Only Subscriber App
echo ========================================
echo.

cd /d "%~dp0"

echo Step 1: Cleaning previous builds...
call gradlew clean
if %ERRORLEVEL% neq 0 (
    echo Clean failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Building debug APK...
call gradlew assembleDebug
if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Copying APK to Desktop...

REM Create destination folder on Desktop
set DESKTOP=%USERPROFILE%\Desktop
set APK_FOLDER=%DESKTOP%\SubscriberApp_APK
if not exist "%APK_FOLDER%" mkdir "%APK_FOLDER%"

REM Copy the APK file
set SOURCE_APK=app\build\outputs\apk\debug\app-debug.apk
set DEST_APK=%APK_FOLDER%\SubscriberApp-Frontend-Only.apk

if exist "%SOURCE_APK%" (
    copy "%SOURCE_APK%" "%DEST_APK%"
    echo.
    echo ========================================
    echo ✅ BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo APK Location: %DEST_APK%
    echo APK Size: 
    for %%A in ("%DEST_APK%") do echo %%~zA bytes
    echo.
    echo The APK has been saved to your Desktop in the folder:
    echo %APK_FOLDER%
    echo.
    echo You can now:
    echo 1. Install it on your Android device
    echo 2. Share it with others
    echo 3. Test all the frontend features
    echo.
    echo Demo Login Credentials:
    echo - admin / password123
    echo - user / demo123  
    echo - test / test123
    echo - guest / guest123
    echo.
    
    REM Open the folder containing the APK
    explorer "%APK_FOLDER%"
    
) else (
    echo ❌ APK file not found at: %SOURCE_APK%
    echo Build may have failed or APK location changed.
)

echo.
echo Press any key to exit...
pause >nul

# 🚀 Build Instructions - Frontend-Only Subscriber App

## 📱 Quick Build Guide

### Method 1: Using Android Studio (Recommended)

1. **Open Android Studio**
2. **Open Project**: Select the `subscriber-android` folder
3. **Wait for Sync**: Let Android Studio sync the project
4. **Build APK**: 
   - Go to `Build` menu → `Build Bundle(s) / APK(s)` → `Build APK(s)`
   - Or use keyboard shortcut: `Ctrl+Shift+A` → type "Build APK"
5. **Locate APK**: Click "locate" in the build notification
6. **Copy to Desktop**: The APK will be at `app/build/outputs/apk/debug/app-debug.apk`

### Method 2: Using Command Line (If Gradle is available)

```bash
# Navigate to project directory
cd subscriber-android

# Build the APK
gradlew assembleDebug

# APK will be created at: app/build/outputs/apk/debug/app-debug.apk
```

### Method 3: Using Build Scripts

Run one of these scripts in the `subscriber-android` folder:
- **Windows**: Double-click `build-apk-simple.bat`
- **PowerShell**: Right-click → "Run with PowerShell" on `build-and-copy-apk.ps1`

## 📁 APK Location

After successful build, the APK will be located at:
```
subscriber-android/app/build/outputs/apk/debug/app-debug.apk
```

The build scripts will automatically copy it to:
```
Desktop/SubscriberApp_APK/SubscriberApp-Frontend-Only.apk
```

## 🔑 Demo Login Credentials

The app displays these credentials on the login screen:

| Username | Password | Role |
|----------|----------|------|
| `admin` | `password123` | Administrator |
| `user` | `demo123` | Regular User |
| `test` | `test123` | Test Account |
| `guest` | `guest123` | Guest Account |

## 📱 Installation on Android Device

1. **Enable Unknown Sources**:
   - Go to Settings → Security → Unknown Sources (Enable)
   - Or Settings → Apps → Special Access → Install Unknown Apps

2. **Transfer APK**:
   - Copy APK to your Android device
   - Or use ADB: `adb install SubscriberApp-Frontend-Only.apk`

3. **Install**:
   - Tap the APK file on your device
   - Follow installation prompts

## ✨ App Features

### 🔐 Authentication
- Beautiful neon-themed login screen
- Hardcoded demo credentials displayed
- Mock authentication system

### 📊 Dashboard
- Professional gradient design
- Real-time clock display
- Navigation to all features

### 📱 Attendance Features
- **QR Scanner**: Mock camera with cycling results
- **NFC Check-in**: Simulated NFC detection
- **WiFi Check-in**: Mock network connection
- **Bluetooth**: Beacon scanning simulation

### 🏠 IOT Smart Home
- **IOT Dashboard**: Overview of all devices
- **Smart Lighting**: Complete lighting control
  - Individual light controls
  - Brightness adjustment
  - Color selection
  - Quick control buttons
- **Professional UI**: Neon gradients and smooth animations

### 👤 User Management
- **Profile Screen**: User information and settings
- **Attendance History**: Mock records with filtering
- **Logout**: Returns to login screen

## 🎨 Design Features

- **Neon Gradient Backgrounds**: Purple, blue, cyan, pink themes
- **Glassmorphism Effects**: Translucent cards with neon borders
- **Smooth Animations**: Loading states and transitions
- **Material Design 3**: Modern Android design system
- **Dark Theme**: Professional dark interface

## 🔧 Technical Details

- **Architecture**: MVVM with Jetpack Compose
- **UI Framework**: Jetpack Compose
- **Navigation**: Navigation Compose
- **State Management**: StateFlow and remember
- **Persistence**: SharedPreferences for cycling state
- **No Network**: Completely offline, no backend required

## 🚀 Cycling Behavior

All check-in methods (QR, NFC, WiFi, Bluetooth) cycle through:
1. **Success** (2 seconds) → 
2. **Expired** (2 seconds) → 
3. **Invalid** (2 seconds) → 
4. **Back to Success** (repeats)

Each method has independent cycling counters stored in SharedPreferences.

## 📋 Troubleshooting

### Build Issues
- **Gradle not found**: Use Android Studio method
- **Sync failed**: Check internet connection for dependencies
- **Build failed**: Clean project and rebuild

### Installation Issues
- **Installation blocked**: Enable Unknown Sources
- **App won't start**: Check Android version (minimum API 24)

### Runtime Issues
- **Screens not loading**: All screens are mock, no backend required
- **Login not working**: Use provided demo credentials
- **Features not responding**: All features are simulated for demo

## 📞 Support

This is a frontend-only demonstration app with:
- ✅ Complete UI/UX implementation
- ✅ Mock data and services
- ✅ Professional design
- ✅ Smooth animations
- ✅ No backend dependencies

Perfect for showcasing modern Android development and UI/UX design capabilities!
